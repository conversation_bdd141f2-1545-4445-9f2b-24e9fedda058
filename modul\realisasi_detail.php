<?php
if (!defined('_emonevadmin_')) {
    header("Location: ../index.php");
    exit;
}

$opd_id = isset($_GET['opd_id']) ? (int)$_GET['opd_id'] : 0;
$tahun = date('Y');

// Get OPD info untuk judul
$opd_query = $koneksi_db->sql_query("SELECT * FROM ".$namadepan."opd WHERE id = '$opd_id'");
$opd = $koneksi_db->sql_fetchrow($opd_query);

// Get realization data with new table structure
$realisasi_query = "SELECT 
    pb.kode_program,
    pb.nama_program,
    kb.kode_kegiatan,
    kb.nama_kegiatan,
    skb.nama_sub_kegiatan,
    skb.target_kinerja,
    skb.satuan,
    SUM(p.pagu) as pagu,
    SUM(p.realisasi) as realisasi,
    SUM(p.sisa) as sisa,
    AVG(p.fisik) as fisik
FROM pagu_realisasi p
LEFT JOIN program_baru pb ON p.program_id = pb.id_program
LEFT JOIN kegiatan_baru kb ON p.kegiatan_id = kb.id_kegiatan
LEFT JOIN sub_kegiatan_baru skb ON p.id_sub_kegiatan = skb.id_sub_kegiatan
WHERE p.opd_id = '$opd_id' 
AND p.tahun = '$tahun'
GROUP BY pb.kode_program, pb.nama_program, kb.kode_kegiatan, kb.nama_kegiatan, 
         skb.nama_sub_kegiatan, skb.target_kinerja, skb.satuan
ORDER BY pb.kode_program, kb.kode_kegiatan";

$realisasi = $koneksi_db->sql_query($realisasi_query);

// Modifikasi query chart untuk akumulasi nilai realisasi per bulan
$chart_query = "WITH RECURSIVE months AS (
    SELECT 1 as bulan
    UNION ALL
    SELECT bulan + 1 FROM months WHERE bulan < 12
),
monthly_data AS (
    SELECT 
        m.bulan,
        COALESCE(SUM(p.realisasi), 0) as realisasi,
        COALESCE(AVG(p.fisik), 0) as rata_fisik
    FROM months m
    LEFT JOIN pagu_realisasi p ON m.bulan = p.bulan 
        AND p.tahun = '$tahun' 
        AND p.opd_id = '$opd_id'
    GROUP BY m.bulan
),
running_totals AS (
    SELECT 
        bulan,
        SUM(realisasi) OVER (ORDER BY bulan) as akum_realisasi,
        rata_fisik
    FROM monthly_data
)
SELECT 
    bulan,
    akum_realisasi as nilai_realisasi,
    rata_fisik as persen_fisik
FROM running_totals
ORDER BY bulan";

$chart_result = $koneksi_db->sql_query($chart_query);
$realisasi_data = array_fill(0, 12, 0);
$fisik_data = array_fill(0, 12, 0);

while($row = $koneksi_db->sql_fetchrow($chart_result)) {
    $bulan_index = (int)$row['bulan'] - 1;
    $realisasi_data[$bulan_index] = floatval($row['nilai_realisasi']);
    $fisik_data[$bulan_index] = floatval($row['persen_fisik']);
}

// Get summary data
$summary_query = "SELECT 
    COUNT(DISTINCT p.program_id) as total_program,
    COUNT(DISTINCT p.kegiatan_id) as total_kegiatan,
    COUNT(DISTINCT p.id_sub_kegiatan) as total_sub_kegiatan,
    SUM(p.pagu) as total_pagu,
    SUM(p.realisasi) as total_realisasi,
    AVG(p.fisik) as rata_fisik
FROM pagu_realisasi p
WHERE p.opd_id = '$opd_id' 
AND p.tahun = '$tahun'";

$summary_result = $koneksi_db->sql_query($summary_query);
$summary = $koneksi_db->sql_fetchrow($summary_result);
?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Detail Realisasi <?php echo $opd['nm_opd']; ?></h1>
            </div>
            <div class="col-sm-6">
                <a href="?page=realisasi" class="btn btn-secondary float-sm-right">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fas fa-bookmark"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Program</span>
                        <span class="info-box-number"><?php echo $summary['total_program']; ?></span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-success"><i class="fas fa-list"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Kegiatan</span>
                        <span class="info-box-number"><?php echo $summary['total_kegiatan']; ?></span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-warning"><i class="fas fa-tasks"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Sub Kegiatan</span>
                        <span class="info-box-number"><?php echo $summary['total_sub_kegiatan']; ?></span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-danger"><i class="fas fa-chart-pie"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Rata-rata Fisik</span>
                        <span class="info-box-number"><?php echo number_format($summary['rata_fisik'], 1); ?>%</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Trend Realisasi Bulanan - <?php echo $opd['nm_opd']; ?></h3>
                    </div>
                    <div class="card-body">
                        <canvas id="realisasiChart" style="min-height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail Program & Kegiatan</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="realisasiTable">
                        <thead>
                            <tr>
                                <th>Kode</th>
                                <th>Program/Kegiatan/Sub Kegiatan</th>
                                <th>Target Kinerja</th>
                                <th>Pagu</th>
                                <th>Realisasi</th>
                                <th>Sisa</th>
                                <th>Keuangan</th>
                                <th>Fisik</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $current_program = '';
                            $current_kegiatan = '';
                            while($row = $koneksi_db->sql_fetchrow($realisasi)) { 
                                $percentage = ($row['pagu'] > 0) ? ($row['realisasi'] / $row['pagu'] * 100) : 0;
                            ?>
                            <tr>
                                <td>
                                    <?php 
                                    if($row['kode_program'] != $current_program) {
                                        echo $row['kode_program'] . "<br>";
                                    }
                                    if($row['kode_kegiatan'] != $current_kegiatan) {
                                        echo $row['kode_kegiatan'];
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php 
                                    if($row['nama_program'] != $current_program) {
                                        echo "<strong>" . $row['nama_program'] . "</strong><br>";
                                    }
                                    if($row['nama_kegiatan'] != $current_kegiatan) {
                                        echo $row['nama_kegiatan'] . "<br>";
                                    }
                                    echo "<small>" . $row['nama_sub_kegiatan'] . "</small>";
                                    ?>
                                </td>
                                <td>
                                    <?php 
                                    echo $row['target_kinerja'];
                                    if($row['satuan']) {
                                        echo " " . $row['satuan'];
                                    }
                                    ?>
                                </td>
                                <td class="text-right"><?php echo number_format($row['pagu'],0); ?></td>
                                <td class="text-right"><?php echo number_format($row['realisasi'],0); ?></td>
                                <td class="text-right"><?php echo number_format($row['sisa'],0); ?></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar bg-primary" role="progressbar" 
                                             style="width: <?php echo number_format($percentage,1); ?>%">
                                            <?php echo number_format($percentage,1); ?>%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: <?php echo number_format($row['fisik'],1); ?>%">
                                            <?php echo number_format($row['fisik'],1); ?>%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php 
                                $current_program = $row['kode_program'];
                                $current_kegiatan = $row['kode_kegiatan'];
                            } 
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(document).ready(function() {
    if (typeof Chart === 'undefined') {
        console.error('Chart.js tidak dimuat!');
        return;
    }

    var ctx = document.getElementById('realisasiChart');
    if (!ctx) {
        console.error('Canvas tidak ditemukan!');
        return;
    }

    var myChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
            datasets: [
                {
                    label: 'Realisasi Keuangan (Rp)',
                    data: <?php echo json_encode($realisasi_data); ?>,
                    borderColor: '#0ea5e9',
                    backgroundColor: 'rgba(14, 165, 233, 0.2)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    yAxisID: 'y'
                },
                {
                    label: 'Realisasi Fisik (%)',
                    data: <?php echo json_encode($fisik_data); ?>,
                    borderColor: '#f43f5e',
                    backgroundColor: 'rgba(244, 63, 94, 0.2)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Realisasi Keuangan (Rp)'
                    },
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                            }).format(value);
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Realisasi Fisik (%)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    },
                    grid: {
                        drawOnChartArea: false
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            let value = context.parsed.y;
                            
                            if (label.includes('Keuangan')) {
                                value = new Intl.NumberFormat('id-ID', {
                                    style: 'currency',
                                    currency: 'IDR',
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0
                                }).format(value);
                            } else {
                                value = value.toFixed(2) + '%';
                            }
                            
                            return label + ': ' + value;
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'Realisasi <?php echo addslashes($opd['nm_opd']); ?>'
                }
            }
        }
    });

    // Initialize DataTable
    $('#realisasiTable').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true
    });
});
</script> 