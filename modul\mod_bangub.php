<?php
if (!defined('_emonevadmin_')) {
    die("Direct access not allowed!");
}

$judul = "Data Bantuan Gubernur";
$path_module = "modul/mod_bangub";

switch($act){
    default:
        $content = "
        <div class='row'>
            <div class='col-xs-12'>
                <div class='box'>
                    <div class='box-header'>
                        <h3 class='box-title'>$judul</h3>
                        <div class='box-tools pull-right'>
                            <a href='?module=bangub&act=add' class='btn btn-primary btn-sm'><i class='fa fa-plus'></i> Tambah Data</a>
                        </div>
                    </div>
                    <div class='box-body'>
                        <table id='example1' class='table table-bordered table-striped'>
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>OPD</th>
                                    <th>Periode</th>
                                    <th>Pagu</th>
                                    <th>Realisasi</th>
                                    <th>Sisa</th>
                                    <th>% <PERSON><PERSON><PERSON></th>
                                    <th>% Fisik</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>";
                            
        $no = 1;
        $query = $koneksi_db->sql_query("SELECT b.*, o.nama_opd 
            FROM ".$namadepan."bangub b 
            LEFT JOIN ".$namadepan."opd o ON b.opd_id = o.id 
            ORDER BY b.tahun DESC, b.bulan DESC");
        while($r = $koneksi_db->sql_fetchrow($query)){
            $content .= "<tr>
                <td>$no</td>
                <td>".$r['nama_opd']."</td>
                <td>".bulan_indo($r['bulan'])." ".$r['tahun']."</td>
                <td align='right'>".number_format($r['pagu_bangub'],2)."</td>
                <td align='right'>".number_format($r['realisasi_bangub'],2)."</td>
                <td align='right'>".number_format($r['sisa_anggaran'],2)."</td>
                <td align='right'>".number_format($r['persen_realisasi'],2)."%</td>
                <td align='right'>".number_format($r['fisik_bangub'],2)."%</td>
                <td>
                    <a href='?module=bangub&act=edit&id=".$r['id']."' class='btn btn-warning btn-xs'><i class='fa fa-edit'></i></a>
                    <a href='javascript:void(0)' onclick='if(confirm(\"Apakah anda yakin ingin menghapus data ini?\")){window.location.href=\"?module=bangub&act=delete&id=".$r['id']."\"}' class='btn btn-danger btn-xs'><i class='fa fa-trash'></i></a>
                </td>
            </tr>";
            $no++;
        }
        
        $content .= "</tbody></table>
                    </div>
                </div>
            </div>
        </div>";
        
        echo $content;
    break;
    
    case "add":
        $content = "
        <div class='row'>
            <div class='col-md-12'>
                <div class='box box-primary'>
                    <div class='box-header with-border'>
                        <h3 class='box-title'>Tambah $judul</h3>
                    </div>
                    <form method='post' action='?module=bangub&act=save'>
                        <div class='box-body'>
                            <div class='form-group'>
                                <label>OPD</label>
                                <select name='opd_id' class='form-control select2' style='width: 100%;' required>
                                    <option value=''>-- Pilih OPD --</option>";
                                    
        $query_opd = $koneksi_db->sql_query("SELECT * FROM ".$namadepan."opd ORDER BY nama_opd ASC");
        while($opd = $koneksi_db->sql_fetchrow($query_opd)){
            $content .= "<option value='".$opd['id']."'>".$opd['nama_opd']."</option>";
        }
                                    
        $content .= "</select>
                            </div>
                            <div class='row'>
                                <div class='col-md-6'>
                                    <div class='form-group'>
                                        <label>Bulan</label>
                                        <select name='bulan' class='form-control' required>
                                            <option value=''>-- Pilih Bulan --</option>";
                                            
        for($i=1; $i<=12; $i++){
            $content .= "<option value='".$i."'>".bulan_indo($i)."</option>";
        }
                                            
        $content .= "</select>
                                    </div>
                                </div>
                                <div class='col-md-6'>
                                    <div class='form-group'>
                                        <label>Tahun</label>
                                        <select name='tahun' class='form-control' required>
                                            <option value=''>-- Pilih Tahun --</option>";
                                            
        $tahun_sekarang = date('Y');
        for($t=$tahun_sekarang-5; $t<=$tahun_sekarang+5; $t++){
            $content .= "<option value='".$t."' ".($t == $tahun_sekarang ? "selected" : "").">".$t."</option>";
        }
                                            
        $content .= "</select>
                                    </div>
                                </div>
                            </div>
                            <div class='form-group'>
                                <label>Pagu Anggaran</label>
                                <input type='number' name='pagu_bangub' class='form-control' step='0.01' required>
                            </div>
                            <div class='form-group'>
                                <label>Realisasi Anggaran</label>
                                <input type='number' name='realisasi_bangub' class='form-control' step='0.01' required>
                            </div>
                            <div class='form-group'>
                                <label>Persentase Fisik (%)</label>
                                <input type='number' name='fisik_bangub' class='form-control' step='0.01' max='100' required>
                            </div>
                        </div>
                        <div class='box-footer'>
                            <button type='submit' class='btn btn-primary'><i class='fa fa-save'></i> Simpan</button>
                            <a href='?module=bangub' class='btn btn-default'><i class='fa fa-arrow-left'></i> Kembali</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>";
        
        echo $content;
    break;
    
    case "edit":
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        $query = $koneksi_db->sql_query("SELECT * FROM ".$namadepan."bangub WHERE id = '$id'");
        $r = $koneksi_db->sql_fetchrow($query);
        
        if(!$r){
            echo "<script>alert('Data tidak ditemukan!'); window.location.href='?module=bangub';</script>";
            exit;
        }
        
        $content = "
        <div class='row'>
            <div class='col-md-12'>
                <div class='box box-primary'>
                    <div class='box-header with-border'>
                        <h3 class='box-title'>Edit $judul</h3>
                    </div>
                    <form method='post' action='?module=bangub&act=save'>
                        <input type='hidden' name='id' value='".$r['id']."'>
                        <div class='box-body'>
                            <div class='form-group'>
                                <label>OPD</label>
                                <select name='opd_id' class='form-control select2' style='width: 100%;' required>
                                    <option value=''>-- Pilih OPD --</option>";
                                    
        $query_opd = $koneksi_db->sql_query("SELECT * FROM ".$namadepan."opd ORDER BY nama_opd ASC");
        while($opd = $koneksi_db->sql_fetchrow($query_opd)){
            $selected = ($opd['id'] == $r['opd_id']) ? "selected" : "";
            $content .= "<option value='".$opd['id']."' $selected>".$opd['nama_opd']."</option>";
        }
                                    
        $content .= "</select>
                            </div>
                            <div class='row'>
                                <div class='col-md-6'>
                                    <div class='form-group'>
                                        <label>Bulan</label>
                                        <select name='bulan' class='form-control' required>
                                            <option value=''>-- Pilih Bulan --</option>";
                                            
        for($i=1; $i<=12; $i++){
            $selected = ($i == $r['bulan']) ? "selected" : "";
            $content .= "<option value='".$i."' $selected>".bulan_indo($i)."</option>";
        }
                                            
        $content .= "</select>
                                    </div>
                                </div>
                                <div class='col-md-6'>
                                    <div class='form-group'>
                                        <label>Tahun</label>
                                        <select name='tahun' class='form-control' required>
                                            <option value=''>-- Pilih Tahun --</option>";
                                            
        $tahun_sekarang = date('Y');
        for($t=$tahun_sekarang-5; $t<=$tahun_sekarang+5; $t++){
            $selected = ($t == $r['tahun']) ? "selected" : "";
            $content .= "<option value='".$t."' $selected>".$t."</option>";
        }
                                            
        $content .= "</select>
                                    </div>
                                </div>
                            </div>
                            <div class='form-group'>
                                <label>Pagu Anggaran</label>
                                <input type='number' name='pagu_bangub' class='form-control' step='0.01' value='".$r['pagu_bangub']."' required>
                            </div>
                            <div class='form-group'>
                                <label>Realisasi Anggaran</label>
                                <input type='number' name='realisasi_bangub' class='form-control' step='0.01' value='".$r['realisasi_bangub']."' required>
                            </div>
                            <div class='form-group'>
                                <label>Persentase Fisik (%)</label>
                                <input type='number' name='fisik_bangub' class='form-control' step='0.01' max='100' value='".$r['fisik_bangub']."' required>
                            </div>
                        </div>
                        <div class='box-footer'>
                            <button type='submit' class='btn btn-primary'><i class='fa fa-save'></i> Simpan</button>
                            <a href='?module=bangub' class='btn btn-default'><i class='fa fa-arrow-left'></i> Kembali</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>";
        
        echo $content;
    break;
    
    case "save":
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $opd_id = intval($_POST['opd_id']);
        $bulan = intval($_POST['bulan']);
        $tahun = intval($_POST['tahun']);
        $pagu_bangub = floatval($_POST['pagu_bangub']);
        $realisasi_bangub = floatval($_POST['realisasi_bangub']);
        $fisik_bangub = floatval($_POST['fisik_bangub']);
        
        // Hitung sisa anggaran dan persentase realisasi
        $sisa_anggaran = $pagu_bangub - $realisasi_bangub;
        $persen_realisasi = ($pagu_bangub > 0) ? ($realisasi_bangub / $pagu_bangub * 100) : 0;
        
        if($id > 0){
            // Update data
            $query = $koneksi_db->sql_query("UPDATE ".$namadepan."bangub SET 
                opd_id = '$opd_id',
                bulan = '$bulan',
                tahun = '$tahun',
                pagu_bangub = '$pagu_bangub',
                realisasi_bangub = '$realisasi_bangub',
                sisa_anggaran = '$sisa_anggaran',
                persen_realisasi = '$persen_realisasi',
                fisik_bangub = '$fisik_bangub'
                WHERE id = '$id'");
                
            if($query){
                echo "<script>alert('Data berhasil diperbarui!'); window.location.href='?module=bangub';</script>";
            }else{
                echo "<script>alert('Gagal memperbarui data!'); window.location.href='?module=bangub';</script>";
            }
        }else{
            // Cek apakah data sudah ada
            $cek = $koneksi_db->sql_query("SELECT id FROM ".$namadepan."bangub WHERE opd_id = '$opd_id' AND bulan = '$bulan' AND tahun = '$tahun'");
            if($koneksi_db->sql_numrows($cek) > 0){
                echo "<script>alert('Data untuk OPD, bulan, dan tahun tersebut sudah ada!'); window.history.back();</script>";
                exit;
            }
            
            // Insert data baru
            $query = $koneksi_db->sql_query("INSERT INTO ".$namadepan."bangub (
                opd_id, bulan, tahun, pagu_bangub, realisasi_bangub, sisa_anggaran, persen_realisasi, fisik_bangub
            ) VALUES (
                '$opd_id', '$bulan', '$tahun', '$pagu_bangub', '$realisasi_bangub', '$sisa_anggaran', '$persen_realisasi', '$fisik_bangub'
            )");
            
            if($query){
                echo "<script>alert('Data berhasil disimpan!'); window.location.href='?module=bangub';</script>";
            }else{
                echo "<script>alert('Gagal menyimpan data!'); window.location.href='?module=bangub';</script>";
            }
        }
    break;
    
    case "delete":
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        $query = $koneksi_db->sql_query("DELETE FROM ".$namadepan."bangub WHERE id = '$id'");
        
        if($query){
            echo "<script>alert('Data berhasil dihapus!'); window.location.href='?module=bangub';</script>";
        }else{
            echo "<script>alert('Gagal menghapus data!'); window.location.href='?module=bangub';</script>";
        }
    break;
}
?>