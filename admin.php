<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();
define('_emonevadmin_',1);

// Perbaiki path include
if (!@include("../../inc/config_admin.php")) {
    echo "Error: File config_admin.php tidak ditemukan.";
    exit();
}

if (!@include("../../inc/database.php")) {
    echo "Error: File database.php tidak ditemukan.";
    exit();
}

if (!@include("../../inc/fungsi.php")) {
    echo "Error: File fungsi.php tidak ditemukan.";
    exit();
}

if (!@include("../../kamus/bhs_id.php")) {
    echo "Error: File bhs_id.php tidak ditemukan.";
    exit();
}

ob_start();
$extfile = "php";
session_set_cookie_params(0, "", "",0);

// Check login
if (!isset($_SESSION['admin_siepra'])) {
    header("Location: ../login.php");
    exit();
}

// Database connection check
if (!$koneksi_db) {
    echo "Error: Koneksi database gagal";
    exit();
}

// Get user data
$userquery = $koneksi_db->sql_query("SELECT * FROM ".$namadepan."admin WHERE userid = '".$_SESSION['admin_siepra']."'");
if (!$userquery) {
    echo "Error: Query gagal";
    exit();
}
$userdata = $koneksi_db->sql_fetchrow($userquery);

$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';
?>
<!DOCTYPE html>
<html lang='en'>
<head>
    <title>e-Monev - Admin</title>
    <meta content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' name='viewport'>
    <link href='../../assets/images/meta_icons/favicon.ico' rel='shortcut icon' type='image/x-icon'>

    <!-- Google Font: Source Sans Pro -->
    <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <!-- Theme style -->
    <link href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css" rel="stylesheet">

    <style>
    /* Sidebar & Brand */
    .main-sidebar {
        background: #1e293b;
        min-height: 100vh;  /* Hapus ini */
        width: 250px;
        position: fixed;
        box-shadow: inset -10px 0 15px -10px rgba(0,0,0,0.3);
        height: 100%;  /* Tambah ini */
        bottom: 0;     /* Tambah ini */
    }

    /* Wrapper untuk konten utama */
    .content-wrapper {
        min-height: 100vh;  /* Tambah ini */
        background: #f4f6f9;
        margin-left: 250px;  /* Sesuaikan dengan lebar sidebar */
    }

    /* Body dan HTML */
    html, body {
        height: 100%;
        margin: 0;
        background: #f4f6f9;
    }

    /* Wrapper utama */
    .wrapper {
        min-height: 100%;
        position: relative;
        overflow-x: hidden;
        overflow-y: auto;
    }

    /* Sidebar inner */
    .sidebar {
        height: calc(100% - 4rem);  /* Tambah ini - tinggi total dikurangi header */
        overflow-y: auto;           /* Tambah ini - untuk scroll jika menu terlalu panjang */
        padding-bottom: 20px;       /* Tambah ini - untuk spacing di bawah */
    }

    /* Footer */
    .main-footer {
        margin-left: 250px;  /* Sesuaikan dengan lebar sidebar */
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        background: white;
    }

    .brand-link { padding: 1.5rem; display: flex; align-items: center; border: none !important; }
    .brand-link img { width: 35px; height: 35px; margin-right: 10px; }
    .brand-link .brand-text { color: #fff; font-size: 1.25rem; font-weight: 600; }

    /* User Panel */
    .user-panel { padding: 1rem; border-bottom: 1px solid rgba(255,255,255,.1); }
    .user-panel img { width: 40px; height: 40px; }
    .user-panel .info { margin-left: 10px; }
    .user-panel .info a { color: #fff; font-weight: 500; }
    .user-panel .info small { color: rgba(255,255,255,.6); }

    /* Navigation */
    .nav-sidebar .nav-item { margin: 4px 8px; }
    .nav-sidebar .nav-link {
        padding: 12px 16px;
        color: rgba(255,255,255,.7);
        display: flex;
        align-items: center;
        border-radius: 8px;
        transition: all 0.3s ease;
        transform: translateZ(0);
        box-shadow: 0 0 1px rgba(255,255,255,0);
    }

    .nav-sidebar .nav-link:hover {
        background: #0284c7;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }

    .nav-sidebar .nav-link.active {
        background: #0284c7;
        color: #fff;
        box-shadow: inset 0 2px 5px rgba(0,0,0,0.2);
    }

    .nav-sidebar .nav-link i {
        margin-right: 12px;
        width: 20px;
        text-align: center;
        transition: transform 0.3s ease;
    }

    .nav-sidebar .nav-link:hover i {
        transform: scale(1.2);
    }

    /* Dropdown/Treeview */
    .nav-treeview {
        padding: 0;
        margin: 0 4px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .nav-treeview .nav-item {
        margin: 4px 0;
        position: relative;
    }

    .nav-treeview .nav-link {
        padding: 10px 16px 10px 25px;
        margin-left: 12px;
        border-left: 2px solid rgba(255,255,255,0.1);
    }

    .nav-treeview .nav-link:before {
        content: '';
        position: absolute;
        left: -2px;
        top: 0;
        height: 100%;
        width: 2px;
        background: #0284c7;
        transform: scaleY(0);
        transition: transform 0.3s ease;
    }

    .nav-treeview .nav-link:hover:before,
    .nav-treeview .nav-link.active:before {
        transform: scaleY(1);
    }

    .nav-treeview .nav-link i {
        font-size: 8px;
        margin-right: 8px;
    }

    /* Active States & Animations */
    .nav-sidebar .nav-link .right {
        margin-left: auto;
        transition: transform 0.3s ease;
    }

    .nav-item.menu-open > .nav-link .right {
        transform: rotate(-90deg);
    }

    .menu-open > .nav-treeview {
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        0% {
            opacity: 0;
            transform: translateY(-10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Hover Effects */
    .nav-link {
        position: relative;
        overflow: hidden;
    }

    .nav-link:after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255,255,255,.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }

    .nav-link:hover:after {
        animation: ripple 1s ease-out;
    }

    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(40, 40);
            opacity: 0;
        }
    }
    </style>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Bootstrap & AdminLTE -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
</head>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <!-- Main Header -->
        <header class="main-header">
            <nav class="navbar">
                <ul class="nav">
                    <li class="nav-item">
                        <a class="nav-link" data-widget="pushmenu" href="#"><i class="fa fa-bars"></i></a>
                    </li>
                </ul>
                <ul class="nav ml-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link" data-toggle="dropdown" href="#">
                            <i class="fa fa-bell"></i>
                            <span class="badge badge-warning">15</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                            <span class="dropdown-item dropdown-header">15 Notifications</span>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item">
                                <i class="fa fa-envelope mr-2"></i> 4 new messages
                            </a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php">
                            <i class="fa fa-power-off"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </header>

        <!-- Main Sidebar -->
        <aside class="main-sidebar">
            <a href="index.php" class="brand-link">
                <img src="https://s1creative.com/siepra/opd/opd/2025/img/logo.png" alt="Logo">
                <span class="brand-text">SIEPRA</span>
            </a>
            <div class="sidebar">

<div class="user-panel d-flex align-items-center p-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="image">
        <i class="fas fa-user-circle fa-2x" style="color: white;"></i>
    </div>
    <div class="info ml-3">
        <a href="#" class="d-block text-white">Admin SIEMONE</a>
        <small class="text-white-50">Administrator</small>
    </div>
</div>

                <nav class="mt-3">
                    <ul class="nav nav-pills nav-sidebar flex-column">
                        <li class="nav-item">
                            <a href="?page=dashboard" class="nav-link <?php echo ($page == 'dashboard') ? 'active' : ''; ?>">
                                <i class="fas fa-tachometer-alt"></i>
                                <p>Dashboard</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="?page=realisasi_opd" class="nav-link <?php echo ($page == 'realisasi_opd') ? 'active' : ''; ?>">
                                <i class="fas fa-chart-bar"></i>
                                <p>Data Realisasi OPD</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="?page=posisi_aktif" class="nav-link <?php echo ($page == 'posisi_aktif') ? 'active' : ''; ?>">
                                <i class="fas fa-clock"></i>
                                <p>Pengaturan Periode</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="?page=realisasi_bulanan" class="nav-link <?php echo ($page == 'realisasi_bulanan') ? 'active' : ''; ?>">
                                <i class="nav-icon fas fa-calendar"></i>
                                <p>Realisasi Bulanan</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="?page=opd_management" class="nav-link <?php echo (in_array($page, ['opd_management', 'opd_detail'])) ? 'active' : ''; ?>">
                                <i class="fas fa-building"></i>
                                <p>Manajemen OPD</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="?page=data_anggaran" class="nav-link <?php echo ($page == 'data_anggaran') ? 'active' : ''; ?>">
                                <i class="fas fa-money-bill-wave"></i>
                                <p>Data Anggaran</p>
                            </a>
                        </li>
                        
                        <!-- Rekap Dana Pendapatan Menu Item -->
                        <li class="nav-item">
                            <a href="?page=rekap_pendapatan" class="nav-link <?php echo ($page == 'rekap_pendapatan') ? 'active' : ''; ?>">
                                <i class="fas fa-chart-pie"></i>
                                <p>Rekap Dana Pendapatan</p>
                            </a>
                        </li>
                        
                        <!-- Add Bangub Menu Item -->
                        <li class="nav-item">
                            <a href="?page=bangub" class="nav-link <?php echo ($page == 'bangub') ? 'active' : ''; ?>">
                                <i class="fas fa-hand-holding-usd"></i>
                                <p>Bantuan Gubernur</p>
                            </a>
                        </li>

                        <li class="nav-item <?php echo (in_array($page, ['laporan_realisasi', 'rekap_opd'])) ? 'menu-open' : ''; ?>">
                            <a href="#" class="nav-link">
                                <i class="fas fa-file-alt"></i>
                                <p>Laporan</p>
                                <i class="fas fa-angle-left right"></i>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="?page=laporan_realisasi" class="nav-link <?php echo ($page == 'laporan_realisasi') ? 'active' : ''; ?>">
                                        <i class="far fa-circle"></i>
                                        <p>Laporan Realisasi</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="?page=rekap_opd" class="nav-link <?php echo ($page == 'rekap_opd') ? 'active' : ''; ?>">
                                        <i class="far fa-circle"></i>
                                        <p>Rekap per OPD</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <?php include "modul/$page.php"; ?>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
            <strong>Copyright &copy; 2024 Bagian Administrasi Pembangunan Setda Kota Palembang</strong>
        </footer>
    </div>

    <!-- Scripts -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap4.min.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap4.min.css">
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <!-- XLSX for Excel export -->
    <script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>

    <script>
    $(document).ready(function() {
        // Toggle sidebar
        $('[data-widget="pushmenu"]').click(function(e) {
            e.preventDefault();
            $('body').toggleClass('sidebar-collapse');
        });

        // Dropdown menu
        $('.nav-sidebar .nav-item > .nav-link').click(function(e) {
            var $item = $(this).parent('.nav-item');
            if($item.find('.nav-treeview').length) {
                e.preventDefault();

                if($item.hasClass('menu-open')) {
                    $item.removeClass('menu-open');
                    $item.find('.nav-treeview').slideUp(300);
                } else {
                    // Close other open menus
                    $('.nav-item.menu-open').removeClass('menu-open').find('.nav-treeview').slideUp(300);

                    // Open clicked menu
                    $item.addClass('menu-open');
                    $item.find('.nav-treeview').slideDown(300);
                }
            }
        });

        // Keep submenu open for active items
        $('.nav-treeview .nav-link.active').parents('.nav-item').addClass('menu-open').find('.nav-treeview').show();

        // Dropdown notifications
        $('.dropdown').on('show.bs.dropdown', function() {
            $(this).find('.dropdown-menu').first().stop(true, true).slideDown(200);
        });

        $('.dropdown').on('hide.bs.dropdown', function() {
            $(this).find('.dropdown-menu').first().stop(true, true).slideUp(200);
        });

        // Add hover animation for menu items
        $('.nav-link').hover(
            function() {
                $(this).find('i').addClass('animated bounce');
            },
            function() {
                $(this).find('i').removeClass('animated bounce');
            }
        );

        // Smooth scroll for menu clicks
        $('.nav-link').click(function(e) {
            if(!$(this).parent().hasClass('menu-open')) {
                $('.nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });
    });
    </script>
</body>
</html>
<?php ob_end_flush(); ?>