<?php
if (!defined('_emonevadmin_')) {
    header("Location: ../index.php");
    exit;
}

// Handle form submission
if(isset($_POST['update'])) {
    $id = 1; // Fixed ID since we only have one record
    $deskripsi = addslashes(htmlspecialchars($_POST['deskripsi']));
    $tanggal_mulai = htmlspecialchars($_POST['tanggal_mulai']);
    $tanggal_selesai = htmlspecialchars($_POST['tanggal_selesai']);
    $status = htmlspecialchars($_POST['status']);
    
    $update_query = "UPDATE posisi_aktif SET 
        deskripsi = '$deskripsi',
        tanggal_mulai = '$tanggal_mulai',
        tanggal_selesai = '$tanggal_selesai',
        status = '$status'
        WHERE id = $id";
    
    if($koneksi_db->sql_query($update_query)) {
        echo "<div class='alert alert-success'>Berhasil memperbarui pengaturan</div>";
    } else {
        echo "<div class='alert alert-danger'>Gagal memperbarui pengaturan</div>";
    }
}

// Get current settings
$query = $koneksi_db->sql_query("SELECT * FROM posisi_aktif WHERE id = 1");
$data = $koneksi_db->sql_fetchrow($query);
?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Pengaturan Posisi Aktif</h1>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Pengaturan Periode Aktif SIEPRA</h3>
            </div>
            
            <form method="post" action="">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Tanggal Mulai</label>
                                <input type="date" class="form-control" name="tanggal_mulai" 
                                       value="<?php echo $data['tanggal_mulai']; ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label>Tanggal Selesai</label>
                                <input type="date" class="form-control" name="tanggal_selesai" 
                                       value="<?php echo $data['tanggal_selesai']; ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label>Status</label>
                                <select class="form-control" name="status" required>
                                    <option value="aktif" <?php echo ($data['status'] == 'aktif') ? 'selected' : ''; ?>>Aktif</option>
                                    <option value="nonaktif" <?php echo ($data['status'] == 'nonaktif') ? 'selected' : ''; ?>>Non Aktif</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>Deskripsi/Keterangan</label>
                                <textarea class="form-control" name="deskripsi" rows="4"><?php echo $data['deskripsi']; ?></textarea>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> Informasi</h5>
                                <p>Pengaturan ini digunakan untuk mengatur periode aktif SIEPRA.</p>
                                <ul>
                                    <li>Jika status <b>Aktif</b>, maka OPD dapat mengakses SIEPRA sesuai periode yang ditentukan</li>
                                    <li>Jika status <b>Non Aktif</b>, maka OPD tidak dapat mengakses SIEPRA</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian</h5>
                                <p>Pastikan tanggal yang dimasukkan sudah benar sebelum menyimpan perubahan.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" name="update" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Current Status Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Status Saat Ini</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="far fa-calendar-alt"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Tanggal Mulai</span>
                                <span class="info-box-number"><?php echo date('d M Y', strtotime($data['tanggal_mulai'])); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-danger"><i class="far fa-calendar-check"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Tanggal Selesai</span>
                                <span class="info-box-number"><?php echo date('d M Y', strtotime($data['tanggal_selesai'])); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon <?php echo ($data['status'] == 'aktif') ? 'bg-success' : 'bg-warning'; ?>">
                                <i class="fas <?php echo ($data['status'] == 'aktif') ? 'fa-check' : 'fa-ban'; ?>"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Status</span>
                                <span class="info-box-number"><?php echo ucfirst($data['status']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="callout callout-info">
                    <h5>Keterangan:</h5>
                    <p><?php echo nl2br($data['deskripsi']); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(function() {
    // Add date validation
    $('form').submit(function(e) {
        var start = new Date($('input[name="tanggal_mulai"]').val());
        var end = new Date($('input[name="tanggal_selesai"]').val());
        
        if(start > end) {
            e.preventDefault();
            alert('Tanggal mulai tidak boleh lebih besar dari tanggal selesai!');
        }
    });
});
</script> 