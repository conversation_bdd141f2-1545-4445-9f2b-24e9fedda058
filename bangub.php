<?php
include '../layout/header.php';
include '../layout/sidebar.php';
?>

<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Data Bantuan Gubernur</h1>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-tambah">
                                Tambah Data
                            </button>
                            <a href="cetak_bangub.php" target="_blank" class="btn btn-success">
                                <i class="fas fa-print"></i> Cetak
                            </a>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <select name="opd_id" class="form-control">
                                                <option value="">Pilih OPD</option>
                                                <?php
                                                $query_opd = mysqli_query($koneksi, "SELECT * FROM tb_opd ORDER BY nama_opd");
                                                while ($opd = mysqli_fetch_array($query_opd)) {
                                                    $selected = ($_GET['opd_id'] == $opd['id']) ? 'selected' : '';
                                                    echo "<option value='" . $opd['id'] . "' $selected>" . $opd['nama_opd'] . "</option>";
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <select name="bulan" class="form-control">
                                                <option value="">Pilih Bulan</option>
                                                <?php
                                                for ($i = 1; $i <= 12; $i++) {
                                                    $selected = ($_GET['bulan'] == $i) ? 'selected' : '';
                                                    echo "<option value='$i' $selected>" . date('F', mktime(0, 0, 0, $i, 1)) . "</option>";
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <select name="tahun" class="form-control">
                                                <option value="">Pilih Tahun</option>
                                                <?php
                                                $tahun_sekarang = date('Y');
                                                for ($tahun = $tahun_sekarang - 5; $tahun <= $tahun_sekarang + 5; $tahun++) {
                                                    $selected = ($_GET['tahun'] == $tahun) ? 'selected' : '';
                                                    echo "<option value='$tahun' $selected>$tahun</option>";
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary">Filter</button>
                                        <a href="bangub.php" class="btn btn-secondary">Reset</a>
                                    </div>
                                </div>
                            </form>

                            <table id="example1" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>OPD</th>
                                        <th>Periode</th>
                                        <th>Pagu</th>
                                        <th>Realisasi</th>
                                        <th>Sisa</th>
                                        <th>% Realisasi</th>
                                        <th>% Fisik</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $where = "1=1";
                                    if (isset($_GET['opd_id']) && $_GET['opd_id'] != '') {
                                        $where .= " AND opd_id = '" . $_GET['opd_id'] . "'";
                                    }
                                    if (isset($_GET['bulan']) && $_GET['bulan'] != '') {
                                        $where .= " AND bulan = '" . $_GET['bulan'] . "'";
                                    }
                                    if (isset($_GET['tahun']) && $_GET['tahun'] != '') {
                                        $where .= " AND tahun = '" . $_GET['tahun'] . "'";
                                    }

                                    $no = 1;
                                    $query = mysqli_query($koneksi, "SELECT b.*, o.nama_opd 
                                        FROM tb_bangub b 
                                        LEFT JOIN tb_opd o ON b.opd_id = o.id 
                                        WHERE $where 
                                        ORDER BY b.tahun DESC, b.bulan DESC");
                                    while ($data = mysqli_fetch_array($query)) {
                                    ?>
                                        <tr>
                                            <td><?php echo $no++; ?></td>
                                            <td><?php echo $data['nama_opd']; ?></td>
                                            <td><?php echo date('F', mktime(0, 0, 0, $data['bulan'], 1)) . ' ' . $data['tahun']; ?></td>
                                            <td><?php echo number_format($data['pagu_bangub'], 2); ?></td>
                                            <td><?php echo number_format($data['realisasi_bangub'], 2); ?></td>
                                            <td><?php echo number_format($data['sisa_anggaran'], 2); ?></td>
                                            <td><?php echo number_format($data['persen_realisasi'], 2); ?>%</td>
                                            <td><?php echo number_format($data['fisik_bangub'], 2); ?>%</td>
                                            <td>
                                                <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#modal-edit<?php echo $data['id']; ?>">
                                                    Edit
                                                </button>
                                                <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#modal-hapus<?php echo $data['id']; ?>">
                                                    Hapus
                                                </button>
                                            </td>
                                        </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="modal-tambah">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="bangub_proses.php" enctype="multipart/form-data">
                <div class="modal-header">
                    <h4 class="modal-title">Tambah Data Bangub</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>OPD</label>
                        <select name="opd_id" class="form-control" required>
                            <option value="">Pilih OPD</option>
                            <?php
                            $query_opd = mysqli_query($koneksi, "SELECT * FROM tb_opd ORDER BY nama_opd");
                            while ($opd = mysqli_fetch_array($query_opd)) {
                                echo "<option value='" . $opd['id'] . "'>" . $opd['nama_opd'] . "</option>";
                            }
                            ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Bulan</label>
                        <select name="bulan" class="form-control" required>
                            <?php
                            for ($i = 1; $i <= 12; $i++) {
                                echo "<option value='$i'>" . date('F', mktime(0, 0, 0, $i, 1)) . "</option>";
                            }
                            ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tahun</label>
                        <input type="text" name="tahun" class="form-control" required value="<?php echo date('Y'); ?>">
                    </div>
                    <div class="form-group">
                        <label>Pagu Bangub</label>
                        <input type="number" step="0.01" name="pagu_bangub" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Realisasi Bangub</label>
                        <input type="number" step="0.01" name="realisasi_bangub" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Fisik Bangub (%)</label>
                        <input type="number" step="0.01" name="fisik_bangub" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Keterangan</label>
                        <textarea name="keterangan" class="form-control"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Gambar</label>
                        <input type="file" name="gambar" class="form-control">
                    </div>
                </div>
                <div class="modal-footer justify-content-between">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Tutup</button>
                    <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../layout/footer.php'; ?>