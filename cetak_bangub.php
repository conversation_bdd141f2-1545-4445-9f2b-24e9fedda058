<?php
include '../config/koneksi.php';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Cetak Data Bantuan Gubernur</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid black;
            padding: 5px;
        }
        th {
            background-color: #f2f2f2;
        }
        h2 {
            text-align: center;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body onload="window.print();">
    <div class="header">
        <h2>LAPORAN DATA BANTUAN GUBERNUR</h2>
        <p>Periode: <?php 
            if (isset($_GET['bulan'])) {
                echo date('F', mktime(0, 0, 0, $_GET['bulan'], 1)) . ' ';
            }
            echo isset($_GET['tahun']) ? $_GET['tahun'] : date('Y');
        ?></p>
    </div>

    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>OPD</th>
                <th>Periode</th>
                <th>Pagu</th>
                <th>Realisasi</th>
                <th>Sisa</th>
                <th>% Realisasi</th>
                <th>% Fisik</th>
                <th>Keterangan</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $where = "1=1";
            if (isset($_GET['opd_id']) && $_GET['opd_id'] != '') {
                $where .= " AND opd_id = '" . $_GET['opd_id'] . "'";
            }
            if (isset($_GET['bulan']) && $_GET['bulan'] != '') {
                $where .= " AND bulan = '" . $_GET['bulan'] . "'";
            }
            if (isset($_GET['tahun']) && $_GET['tahun'] != '') {
                $where .= " AND tahun = '" . $_GET['tahun'] . "'";
            }

            $no = 1;
            $query = mysqli_query($koneksi, "SELECT b.*, o.nama_opd 
                FROM tb_bangub b 
                LEFT JOIN tb_opd o ON b.opd_id = o.id 
                WHERE $where 
                ORDER BY b.tahun DESC, b.bulan DESC");
            
            $total_pagu = 0;
            $total_realisasi = 0;
            
            while ($data = mysqli_fetch_array($query)) {
                $total_pagu += $data['pagu_bangub'];
                $total_realisasi += $data['realisasi_bangub'];
            ?>
                <tr>
                    <td><?php echo $no++; ?></td>
                    <td><?php echo $data['nama_opd']; ?></td>
                    <td><?php echo date('F', mktime(0, 0, 0, $data['bulan'], 1)) . ' ' . $data['tahun']; ?></td>
                    <td align="right"><?php echo number_format($data['pagu_bangub'], 2); ?></td>
                    <td align="right"><?php echo number_format($data['realisasi_bangub'], 2); ?></td>
                    <td align="right"><?php echo number_format($data['sisa_anggaran'], 2); ?></td>
                    <td align="right"><?php echo number_format($data['persen_realisasi'], 2); ?>%</td>
                    <td align="right"><?php echo number_format($data['fisik_bangub'], 2); ?>%</td>
                    <td><?php echo $data['keterangan']; ?></td>
                </tr>
            <?php } ?>
            <tr>
                <td colspan="3" align="center"><strong>Total</strong></td>
                <td align="right"><strong><?php echo number_format($total_pagu, 2); ?></strong></td>
                <td align="right"><strong><?php echo number_format($total_realisasi, 2); ?></strong></td>
                <td align="right"><strong><?php echo number_format($total_pagu - $total_realisasi, 2); ?></strong></td>
                <td align="right"><strong><?php echo $total_pagu > 0 ? number_format(($total_realisasi / $total_pagu) * 100, 2) : '0.00'; ?>%</strong></td>
                <td colspan="2"></td>
            </tr>
        </tbody>
    </table>

    <div style="text-align: right; margin-top: 20px;">
        <p>
            <?php echo date('d F Y'); ?><br>
            Mengetahui,<br><br><br><br>
            _________________
        </p>
    </div>
</body>
</html>