<?php
if (!defined('_emonevadmin_')) {
    header("Location: index.php");
    exit;
}

// Get OPD data with related information from tb_bangub and fisikmj tables
$query = "SELECT
    o.id,
    o.nm_opd,
    o.alamat,
    o.telp,
    o.email,
    o.website,
    o.logo,
    o.aktif,
    COUNT(DISTINCT b.id) AS total_bangub,
    SUM(b.pagu_bangub) AS total_pagu_bangub,
    SUM(b.realisasi_bangub) AS total_realisasi_bangub,
    COUNT(DISTINCT f.id) AS total_fisik,
    SUM(f.nilai_dpa) AS total_nilai_dpa,
    SUM(f.realisasi) AS total_realisasi_fisik
FROM siepra_opd o
LEFT JOIN tb_bangub b ON o.id = b.opd_id
LEFT JOIN fisikmj f ON o.id = f.opd_id
GROUP BY o.id, o.nm_opd, o.alamat, o.telp, o.email, o.website, o.logo, o.aktif
ORDER BY o.nm_opd";

$result = $koneksi_db->sql_query($query);

// Handle activation/deactivation
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $id = (int)$_GET['id'];

    if ($action === 'toggle_status') {
        // Get current status
        $status_query = "SELECT aktif FROM siepra_opd WHERE id = $id";
        $status_result = $koneksi_db->sql_query($status_query);
        $status_row = $koneksi_db->sql_fetchrow($status_result);

        // Toggle status
        $new_status = ($status_row['aktif'] == 'y') ? 'n' : 'y';
        $update_query = "UPDATE siepra_opd SET aktif = '$new_status' WHERE id = $id";

        if ($koneksi_db->sql_query($update_query)) {
            echo "<script>
                alert('Status OPD berhasil diubah');
                window.location.href = '?page=opd_management';
            </script>";
        } else {
            echo "<script>
                alert('Gagal mengubah status OPD');
                window.location.href = '?page=opd_management';
            </script>";
        }
    }
}
?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Manajemen OPD</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="?page=dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Manajemen OPD</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Daftar Organisasi Perangkat Daerah (OPD)</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                        <i class="fas fa-file-excel mr-2"></i>Export Excel
                    </button>
                    <button type="button" class="btn btn-primary btn-sm ml-2" onclick="printTable()">
                        <i class="fas fa-print mr-2"></i>Cetak
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="opdTable">
                        <thead>
                            <tr class="text-center bg-primary">
                                <th>No.</th>
                                <th>Nama OPD</th>
                                <th>Alamat</th>
                                <th>Kontak</th>
                                <th>Data Bangunan</th>
                                <th>Data Fisik</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            while ($row = $koneksi_db->sql_fetchrow($result)) {
                                $status_badge = $row['aktif'] == 'y' ?
                                    '<span class="badge badge-success">Aktif</span>' :
                                    '<span class="badge badge-danger">Non-Aktif</span>';

                                echo "<tr>";
                                echo "<td class='text-center'>{$no}</td>";
                                echo "<td>{$row['nm_opd']}</td>";
                                echo "<td>{$row['alamat']}</td>";
                                echo "<td>
                                        <strong>Telp:</strong> {$row['telp']}<br>
                                        <strong>Email:</strong> {$row['email']}<br>
                                        <strong>Website:</strong> {$row['website']}
                                      </td>";
                                echo "<td>
                                        <strong>Jumlah:</strong> {$row['total_bangub']}<br>
                                        <strong>Pagu:</strong> " . number_format($row['total_pagu_bangub'], 0, ',', '.') . "<br>
                                        <strong>Realisasi:</strong> " . number_format($row['total_realisasi_bangub'], 0, ',', '.') . "
                                      </td>";
                                echo "<td>
                                        <strong>Jumlah:</strong> {$row['total_fisik']}<br>
                                        <strong>Nilai DPA:</strong> " . number_format($row['total_nilai_dpa'], 0, ',', '.') . "<br>
                                        <strong>Realisasi:</strong> " . number_format($row['total_realisasi_fisik'], 0, ',', '.') . "
                                      </td>";
                                echo "<td class='text-center'>{$status_badge}</td>";
                                echo "<td class='text-center'>
                                        <a href='?page=opd_detail&id={$row['id']}' class='btn btn-info btn-sm mb-1'>
                                            <i class='fas fa-eye'></i> Detail
                                        </a>
                                        <a href='?page=opd_management&action=toggle_status&id={$row['id']}' class='btn btn-warning btn-sm mb-1'>
                                            <i class='fas fa-sync-alt'></i> " . ($row['aktif'] == 'y' ? 'Nonaktifkan' : 'Aktifkan') . "
                                        </a>
                                      </td>";
                                echo "</tr>";
                                $no++;
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Summary Card -->
        <div class="row">
            <div class="col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fas fa-building"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total OPD</span>
                        <span class="info-box-number" id="totalOPD">0</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-success"><i class="fas fa-check-circle"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">OPD Aktif</span>
                        <span class="info-box-number" id="activeOPD">0</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-danger"><i class="fas fa-times-circle"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">OPD Non-Aktif</span>
                        <span class="info-box-number" id="inactiveOPD">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Tambahkan CSS untuk print -->
<style type="text/css" media="print">
@media print {
    .no-print, .no-print * {
        display: none !important;
    }
    .card {
        box-shadow: none !important;
        border: none !important;
    }
    .table-bordered td, .table-bordered th {
        border: 1px solid #000 !important;
    }
    body {
        margin: 0;
        padding: 15px;
    }
    .content-header, .main-header, .main-sidebar, .main-footer {
        display: none !important;
    }
    .content-wrapper {
        margin-left: 0 !important;
        padding: 0 !important;
    }
}
</style>

<!-- Script untuk tabel dan export -->
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script>
$(document).ready(function() {
    // Inisialisasi DataTable
    var table = $('#opdTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print"],
        "pageLength": 25,
        "order": [[1, 'asc']],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });

    // Hitung total OPD
    var totalOPD = table.rows().count();
    var activeOPD = table.column(6).data().filter(function(value) {
        return value.includes('badge-success');
    }).count();
    var inactiveOPD = totalOPD - activeOPD;

    $('#totalOPD').text(totalOPD);
    $('#activeOPD').text(activeOPD);
    $('#inactiveOPD').text(inactiveOPD);
});

// Fungsi untuk export ke Excel
function exportToExcel() {
    // Buat workbook baru
    var wb = XLSX.utils.book_new();

    // Data untuk Excel
    var data = [
        ['DAFTAR ORGANISASI PERANGKAT DAERAH (OPD)'],
        [], // Baris kosong
        ['No.', 'Nama OPD', 'Alamat', 'Telepon', 'Email', 'Website',
         'Jumlah Bangunan', 'Pagu Bangunan', 'Realisasi Bangunan',
         'Jumlah Fisik', 'Nilai DPA', 'Realisasi Fisik', 'Status']
    ];

    // Ambil data dari tabel
    $('#opdTable tbody tr').each(function(index) {
        var row = [];
        row.push(index + 1); // Nomor

        // Ambil data dari kolom-kolom
        $(this).find('td').each(function(cellIndex) {
            if (cellIndex === 0) return; // Skip kolom nomor karena sudah ditambahkan
            if (cellIndex === 3) { // Kolom kontak
                var text = $(this).text();
                var telp = text.match(/Telp: (.*?)(?=Email)/)[1].trim();
                var email = text.match(/Email: (.*?)(?=Website)/)[1].trim();
                var website = text.match(/Website: (.*?)$/)[1].trim();

                row.push(telp); // Telepon
                row.push(email); // Email
                row.push(website); // Website
                return;
            }
            if (cellIndex === 4) { // Kolom Data Bangunan
                var text = $(this).text();
                var jumlah = text.match(/Jumlah: (.*?)(?=Pagu)/)[1].trim();
                var pagu = text.match(/Pagu: (.*?)(?=Realisasi)/)[1].trim().replace(/\./g, '');
                var realisasi = text.match(/Realisasi: (.*?)$/)[1].trim().replace(/\./g, '');

                row.push(jumlah); // Jumlah Bangunan
                row.push(pagu); // Pagu Bangunan
                row.push(realisasi); // Realisasi Bangunan
                return;
            }
            if (cellIndex === 5) { // Kolom Data Fisik
                var text = $(this).text();
                var jumlah = text.match(/Jumlah: (.*?)(?=Nilai)/)[1].trim();
                var nilai = text.match(/Nilai DPA: (.*?)(?=Realisasi)/)[1].trim().replace(/\./g, '');
                var realisasi = text.match(/Realisasi: (.*?)$/)[1].trim().replace(/\./g, '');

                row.push(jumlah); // Jumlah Fisik
                row.push(nilai); // Nilai DPA
                row.push(realisasi); // Realisasi Fisik
                return;
            }
            if (cellIndex === 6) { // Kolom status
                row.push($(this).text().trim() === 'Aktif' ? 'Aktif' : 'Non-Aktif');
                return;
            }
            if (cellIndex === 7) return; // Skip kolom aksi

            row.push($(this).text());
        });

        data.push(row);
    });

    // Buat worksheet
    var ws = XLSX.utils.aoa_to_sheet(data);

    // Styling worksheet
    ws['!merges'] = [
        {s: {r: 0, c: 0}, e: {r: 0, c: 12}} // Merge cells untuk judul (sesuaikan dengan jumlah kolom)
    ];

    // Atur lebar kolom
    var wscols = [
        {wch: 5},  // No.
        {wch: 40}, // Nama OPD
        {wch: 40}, // Alamat
        {wch: 15}, // Telepon
        {wch: 25}, // Email
        {wch: 25}, // Website
        {wch: 15}, // Jumlah Bangunan
        {wch: 15}, // Pagu Bangunan
        {wch: 15}, // Realisasi Bangunan
        {wch: 15}, // Jumlah Fisik
        {wch: 15}, // Nilai DPA
        {wch: 15}, // Realisasi Fisik
        {wch: 10}  // Status
    ];
    ws['!cols'] = wscols;

    // Tambahkan worksheet ke workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Daftar OPD');

    // Generate file Excel dan download
    XLSX.writeFile(wb, 'Daftar_OPD.xlsx');
}

// Fungsi untuk mencetak
function printTable() {
    // Simpan konten asli body
    var originalContents = document.body.innerHTML;

    // Siapkan konten untuk dicetak
    var printContents = `
        <div style="text-align: center; margin-bottom: 20px;">
            <h3>DAFTAR ORGANISASI PERANGKAT DAERAH (OPD)</h3>
            <h4>BAGIAN ADMINISTRASI PEMBANGUNAN SETDA KOTA PALEMBANG</h4>
        </div>
    `;

    // Tambahkan tabel
    printContents += document.getElementById('opdTable').outerHTML;

    // Ganti konten body dengan konten yang akan dicetak
    document.body.innerHTML = printContents;

    // Tambahkan style untuk tabel saat dicetak
    var style = document.createElement('style');
    style.innerHTML = `
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid black; padding: 5px; }
        th { background-color: #f4f4f4; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        @media print {
            @page { size: landscape; }
        }
    `;
    document.head.appendChild(style);

    // Cetak
    window.print();

    // Kembalikan konten asli
    document.body.innerHTML = originalContents;

    // Reinisialisasi DataTable
    $(document).ready(function() {
        $('#opdTable').DataTable({
            "responsive": true,
            "lengthChange": true,
            "autoWidth": false,
            "buttons": ["copy", "csv", "excel", "pdf", "print"],
            "pageLength": 25,
            "order": [[1, 'asc']],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        });
    });
}
</script>
