<?php
// Test file untuk memeriksa data bangub
session_start();
define('_emonevadmin_',1);

// Include files
include("../../inc/config_admin.php");
include("../../inc/database.php");

echo "<h2>Test Data Bantuan Gubernur</h2>";

// Test koneksi database
if (!$koneksi_db) {
    echo "<p style='color: red;'>Error: Koneksi database gagal</p>";
    exit;
}

echo "<p style='color: green;'>✓ Koneksi database berhasil</p>";

// Cek tabel tb_bangub
$check_table = $koneksi_db->sql_query("SHOW TABLES LIKE 'tb_bangub'");
if ($koneksi_db->sql_numrows($check_table) > 0) {
    echo "<p style='color: green;'>✓ Tabel tb_bangub ditemukan</p>";
} else {
    echo "<p style='color: red;'>✗ Tabel tb_bangub tidak ditemukan</p>";
    exit;
}

// Cek data di tabel
$count_query = $koneksi_db->sql_query("SELECT COUNT(*) as total FROM tb_bangub");
$count_result = $koneksi_db->sql_fetchrow($count_query);
echo "<p>Total data di tb_bangub: <strong>" . $count_result['total'] . "</strong></p>";

if ($count_result['total'] == 0) {
    echo "<h3>Membuat data sample...</h3>";
    
    // Cek tabel siepra_opd
    $opd_query = $koneksi_db->sql_query("SELECT id, nm_opd FROM siepra_opd WHERE aktif = 'y' LIMIT 5");
    $opd_count = $koneksi_db->sql_numrows($opd_query);
    
    if ($opd_count > 0) {
        echo "<p>Ditemukan $opd_count OPD aktif</p>";
        
        // Insert sample data
        $tahun = date('Y');
        $sample_data = [];
        
        while ($opd = $koneksi_db->sql_fetchrow($opd_query)) {
            for ($bulan = 1; $bulan <= 12; $bulan++) {
                $pagu = rand(100000000, 1000000000); // 100jt - 1M
                $realisasi = rand(50000000, $pagu); // 50% - 100% dari pagu
                $sisa = $pagu - $realisasi;
                $persen = ($pagu > 0) ? ($realisasi / $pagu * 100) : 0;
                $fisik = rand(40, 100); // 40% - 100%
                
                $insert_query = "INSERT INTO tb_bangub 
                    (opd_id, tahun, bulan, pagu_bangub, realisasi_bangub, sisa_anggaran, persen_realisasi, fisik_bangub, keterangan) 
                    VALUES 
                    ('{$opd['id']}', '$tahun', '$bulan', '$pagu', '$realisasi', '$sisa', '$persen', '$fisik', 'Data sample untuk testing')";
                
                if ($koneksi_db->sql_query($insert_query)) {
                    $sample_data[] = "✓ Data {$opd['nm_opd']} bulan $bulan berhasil ditambahkan";
                } else {
                    $sample_data[] = "✗ Error menambahkan data {$opd['nm_opd']} bulan $bulan";
                }
            }
        }
        
        echo "<h4>Hasil insert data sample:</h4>";
        echo "<ul>";
        foreach ($sample_data as $result) {
            echo "<li>$result</li>";
        }
        echo "</ul>";
        
        // Recount
        $count_query = $koneksi_db->sql_query("SELECT COUNT(*) as total FROM tb_bangub");
        $count_result = $koneksi_db->sql_fetchrow($count_query);
        echo "<p><strong>Total data setelah insert: " . $count_result['total'] . "</strong></p>";
        
    } else {
        echo "<p style='color: red;'>Tidak ada OPD aktif ditemukan</p>";
    }
}

// Test query untuk dashboard
echo "<h3>Test Query Dashboard</h3>";
$tahun_filter = date('Y');

$summary_query = "SELECT 
    COUNT(DISTINCT b.opd_id) as total_opd,
    SUM(b.pagu_bangub) as total_pagu,
    SUM(b.realisasi_bangub) as total_realisasi,
    AVG(b.persen_realisasi) as rata_persen,
    AVG(b.fisik_bangub) as rata_fisik
    FROM tb_bangub b 
    WHERE b.tahun = '$tahun_filter'";

$summary_result = $koneksi_db->sql_query($summary_query);
if ($summary_result) {
    $summary = $koneksi_db->sql_fetchrow($summary_result);
    echo "<p>✓ Query summary berhasil:</p>";
    echo "<ul>";
    echo "<li>Total OPD: " . $summary['total_opd'] . "</li>";
    echo "<li>Total Pagu: Rp " . number_format($summary['total_pagu'], 0, ',', '.') . "</li>";
    echo "<li>Total Realisasi: Rp " . number_format($summary['total_realisasi'], 0, ',', '.') . "</li>";
    echo "<li>Rata-rata Persen: " . number_format($summary['rata_persen'], 2) . "%</li>";
    echo "<li>Rata-rata Fisik: " . number_format($summary['rata_fisik'], 2) . "%</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>✗ Query summary gagal</p>";
}

// Test query untuk chart
echo "<h3>Test Query Chart</h3>";
$chart_query = "SELECT 
    o.nm_opd,
    SUM(b.pagu_bangub) as total_pagu,
    SUM(b.realisasi_bangub) as total_realisasi,
    AVG(b.persen_realisasi) as avg_persen
    FROM siepra_opd o
    LEFT JOIN tb_bangub b ON o.id = b.opd_id AND b.tahun = '$tahun_filter'
    WHERE o.aktif = 'y'
    GROUP BY o.id, o.nm_opd
    ORDER BY total_realisasi DESC
    LIMIT 5";

$chart_result = $koneksi_db->sql_query($chart_query);
if ($chart_result) {
    echo "<p>✓ Query chart berhasil:</p>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>OPD</th><th>Total Pagu</th><th>Total Realisasi</th><th>Avg Persen</th></tr>";
    
    while($row = $koneksi_db->sql_fetchrow($chart_result)) {
        echo "<tr>";
        echo "<td>" . $row['nm_opd'] . "</td>";
        echo "<td>Rp " . number_format($row['total_pagu'], 0, ',', '.') . "</td>";
        echo "<td>Rp " . number_format($row['total_realisasi'], 0, ',', '.') . "</td>";
        echo "<td>" . number_format($row['avg_persen'], 2) . "%</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>✗ Query chart gagal</p>";
}

echo "<hr>";
echo "<p><a href='admin.php?page=bangub'>← Kembali ke Dashboard Bangub</a></p>";
?>