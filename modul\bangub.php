<?php
if (!defined('_emonevadmin_')) {
    header("Location: ../login.php");
    exit;
}

$act = isset($_GET['act']) ? $_GET['act'] : '';

switch($act) {
    case 'detail_opd':
        $opd_id = isset($_GET['opd_id']) ? (int)$_GET['opd_id'] : 0;
        $tahun_filter = isset($_GET['tahun']) ? (int)$_GET['tahun'] : date('Y');
        
        // Get OPD info
        $opd_query = "SELECT * FROM siepra_opd WHERE id = $opd_id";
        $opd_result = $koneksi_db->sql_query($opd_query);
        $opd_data = $koneksi_db->sql_fetchrow($opd_result);
        
        if (!$opd_data) {
            echo "<script>alert('OPD tidak ditemukan'); window.location.href='?page=bangub';</script>";
            exit;
        }
        
        // Get bangub data for this OPD
        $bangub_query = "SELECT * FROM tb_bangub WHERE opd_id = $opd_id AND tahun = $tahun_filter ORDER BY bulan";
        $bangub_result = $koneksi_db->sql_query($bangub_query);
        
        // Prepare chart data
        $chart_data = array_fill(0, 12, 0);
        $fisik_data = array_fill(0, 12, 0);
        $total_pagu = 0;
        $total_realisasi = 0;
        
        while ($row = $koneksi_db->sql_fetchrow($bangub_result)) {
            $bulan_index = (int)$row['bulan'] - 1;
            $chart_data[$bulan_index] = (float)$row['realisasi_bangub'];
            $fisik_data[$bulan_index] = (float)$row['fisik_bangub'];
            $total_pagu += $row['pagu_bangub'];
            $total_realisasi += $row['realisasi_bangub'];
        }
        
        // Reset result for table display
        $bangub_result = $koneksi_db->sql_query($bangub_query);
        
        // Array nama bulan
        $nama_bulan = array(
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        );
        ?>
        
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">Detail Bantuan Gubernur - <?php echo $opd_data['nm_opd']; ?></h1>
                    </div>
                    <div class="col-sm-6">
                        <a href="?page=bangub" class="btn btn-secondary float-right">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <section class="content">
            <div class="container-fluid">
                <!-- Filter Tahun -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="">
                            <input type="hidden" name="page" value="bangub">
                            <input type="hidden" name="act" value="detail_opd">
                            <input type="hidden" name="opd_id" value="<?php echo $opd_id; ?>">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Pilih Tahun</label>
                                        <select name="tahun" class="form-control" onchange="this.form.submit()">
                                            <?php
                                            $tahun_sekarang = date('Y');
                                            for ($i = $tahun_sekarang - 5; $i <= $tahun_sekarang + 1; $i++) {
                                                $selected = ($i == $tahun_filter) ? 'selected' : '';
                                                echo "<option value='$i' $selected>$i</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Summary Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="info-box bg-info">
                            <span class="info-box-icon"><i class="fas fa-money-bill-wave"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Pagu</span>
                                <span class="info-box-number">Rp <?php echo number_format($total_pagu, 0, ',', '.'); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-success">
                            <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Realisasi</span>
                                <span class="info-box-number">Rp <?php echo number_format($total_realisasi, 0, ',', '.'); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-warning">
                            <span class="info-box-icon"><i class="fas fa-calculator"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Sisa Anggaran</span>
                                <span class="info-box-number">Rp <?php echo number_format($total_pagu - $total_realisasi, 0, ',', '.'); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-danger">
                            <span class="info-box-icon"><i class="fas fa-percentage"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Persentase Realisasi</span>
                                <span class="info-box-number"><?php echo $total_pagu > 0 ? number_format(($total_realisasi / $total_pagu * 100), 2) : 0; ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Chart -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Grafik Realisasi Bulanan <?php echo $tahun_filter; ?></h3>
                    </div>
                    <div class="card-body">
                        <canvas id="realisasiChart" style="height: 400px;"></canvas>
                    </div>
                </div>
                
                <!-- Detail Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Detail Data Bulanan</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel mr-2"></i>Export Excel
                            </button>
                            <button type="button" class="btn btn-primary btn-sm ml-2" onclick="printTable()">
                                <i class="fas fa-print mr-2"></i>Cetak
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="detailTable">
                                <thead>
                                    <tr class="text-center bg-primary">
                                        <th>No</th>
                                        <th>Bulan</th>
                                        <th>Pagu</th>
                                        <th>Realisasi</th>
                                        <th>Sisa</th>
                                        <th>% Realisasi</th>
                                        <th>% Fisik</th>
                                        <th>Keterangan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    while ($row = $koneksi_db->sql_fetchrow($bangub_result)) {
                                        echo "<tr>";
                                        echo "<td class='text-center'>{$no}</td>";
                                        echo "<td>{$nama_bulan[$row['bulan']]}</td>";
                                        echo "<td class='text-right'>Rp " . number_format($row['pagu_bangub'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>Rp " . number_format($row['realisasi_bangub'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>Rp " . number_format($row['sisa_anggaran'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-primary' role='progressbar' style='width: {$row['persen_realisasi']}%'>";
                                        echo number_format($row['persen_realisasi'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-success' role='progressbar' style='width: {$row['fisik_bangub']}%'>";
                                        echo number_format($row['fisik_bangub'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "<td>{$row['keterangan']}</td>";
                                        echo "</tr>";
                                        $no++;
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#detailTable').DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "pageLength": 12,
                "order": [[1, 'asc']],
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
            
            // Debug untuk detail OPD
            console.log('Detail OPD - Chart.js loaded:', typeof Chart !== 'undefined');
            console.log('Chart Data:', <?php echo json_encode($chart_data); ?>);
            console.log('Fisik Data:', <?php echo json_encode($fisik_data); ?>);
            
            // Chart dengan delay
            setTimeout(function() {
                if (typeof Chart !== 'undefined') {
                    var ctx = document.getElementById('realisasiChart');
                    console.log('Detail Chart Canvas:', ctx);
                    
                    if (ctx) {
                        try {
                            new Chart(ctx, {
                                type: 'line',
                                data: {
                                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                                    datasets: [{
                                        label: 'Realisasi Keuangan (Rp)',
                                        data: <?php echo json_encode($chart_data); ?>,
                                        borderColor: '#36a2eb',
                                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                        borderWidth: 3,
                                        fill: true,
                                        tension: 0.4,
                                        yAxisID: 'y'
                                    }, {
                                        label: 'Realisasi Fisik (%)',
                                        data: <?php echo json_encode($fisik_data); ?>,
                                        borderColor: '#ff6384',
                                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                                        borderWidth: 3,
                                        fill: true,
                                        tension: 0.4,
                                        yAxisID: 'y1'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    interaction: {
                                        mode: 'index',
                                        intersect: false,
                                    },
                                    scales: {
                                        y: {
                                            type: 'linear',
                                            display: true,
                                            position: 'left',
                                            title: {
                                                display: true,
                                                text: 'Realisasi Keuangan (Rp)'
                                            },
                                            ticks: {
                                                callback: function(value) {
                                                    if (value >= 1000000000) {
                                                        return 'Rp ' + (value / 1000000000).toFixed(1) + 'M';
                                                    } else if (value >= 1000000) {
                                                        return 'Rp ' + (value / 1000000).toFixed(1) + 'Jt';
                                                    } else if (value >= 1000) {
                                                        return 'Rp ' + (value / 1000).toFixed(1) + 'K';
                                                    }
                                                    return 'Rp ' + value;
                                                }
                                            }
                                        },
                                        y1: {
                                            type: 'linear',
                                            display: true,
                                            position: 'right',
                                            title: {
                                                display: true,
                                                text: 'Realisasi Fisik (%)'
                                            },
                                            max: 100,
                                            min: 0,
                                            ticks: {
                                                callback: function(value) {
                                                    return value + '%';
                                                }
                                            },
                                            grid: {
                                                drawOnChartArea: false
                                            }
                                        }
                                    },
                                    plugins: {
                                        legend: {
                                            position: 'top'
                                        },
                                        tooltip: {
                                            callbacks: {
                                                label: function(context) {
                                                    if (context.datasetIndex === 0) {
                                                        return 'Realisasi: Rp ' + new Intl.NumberFormat('id-ID').format(context.parsed.y);
                                                    } else {
                                                        return 'Fisik: ' + context.parsed.y + '%';
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            });
                            console.log('Detail Chart created successfully');
                        } catch (error) {
                            console.error('Error creating detail chart:', error);
                        }
                    }
                } else {
                    console.error('Chart.js is not loaded in detail page!');
                }
            }, 1000);
        });
        
        // Export functions
        function exportToExcel() {
            var table = document.getElementById('detailTable');
            var wb = XLSX.utils.table_to_book(table, {sheet: "Detail Bantuan Gubernur"});
            XLSX.writeFile(wb, 'Detail_Bantuan_Gubernur_<?php echo $opd_data['nm_opd']; ?>_<?php echo $tahun_filter; ?>.xlsx');
        }
        
        function printTable() {
            var printContents = document.getElementById('detailTable').outerHTML;
            var originalContents = document.body.innerHTML;
            
            document.body.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <h3>DETAIL BANTUAN GUBERNUR</h3>
                    <h4><?php echo strtoupper($opd_data['nm_opd']); ?></h4>
                    <h4>TAHUN <?php echo $tahun_filter; ?></h4>
                </div>
                ${printContents}
            `;
            
            window.print();
            document.body.innerHTML = originalContents;
            location.reload();
        }
        </script>
        <?php
        break;
        
    case 'cetak':
        $opd_filter = isset($_GET['opd_filter']) ? $_GET['opd_filter'] : '';
        $tahun_filter = isset($_GET['tahun_filter']) ? $_GET['tahun_filter'] : date('Y');
        
        $where = "WHERE b.tahun = '$tahun_filter'";
        if($opd_filter) {
            $where .= " AND b.opd_id='$opd_filter'";
        }
        
        $query = "SELECT b.*, o.nm_opd as nama_opd 
                FROM tb_bangub b
                LEFT JOIN siepra_opd o ON b.opd_id = o.id
                $where
                ORDER BY o.nm_opd ASC, b.bulan ASC";
        $result = $koneksi_db->sql_query($query);
        
        // Array nama bulan untuk cetak
        $nama_bulan = array(
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        );
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Cetak Data Bantuan Gubernur</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h2, h3 { text-align: center; margin: 10px 0; }
                h2 { font-size: 16px; font-weight: bold; }
                h3 { font-size: 14px; font-weight: normal; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                table th, table td { border: 1px solid #000; padding: 8px; font-size: 12px; }
                table th { background-color: #f0f0f0; font-weight: bold; text-align: center; }
                .text-right { text-align: right; }
                .text-center { text-align: center; }
                @media print {
                    @page { size: landscape; margin: 1cm; }
                    body { margin: 0; }
                }
            </style>
        </head>
        <body onload="window.print()">
            <h2>LAPORAN BANTUAN GUBERNUR TAHUN <?php echo $tahun_filter; ?></h2>
            <h3>BAGIAN ADMINISTRASI PEMBANGUNAN SETDA KOTA PALEMBANG</h3>
            <br>
            <table>
                <thead>
                    <tr>
                        <th>No</th>
                        <th>OPD</th>
                        <th>Bulan</th>
                        <th>Pagu</th>
                        <th>Realisasi</th>
                        <th>Sisa</th>
                        <th>% Realisasi</th>
                        <th>% Fisik</th>
                    </tr>
                </thead>
                <tbody>
                <?php
                $no = 1;
                $total_pagu = 0;
                $total_realisasi = 0;
                $total_sisa = 0;
                
                while ($row = $koneksi_db->sql_fetchrow($result)) {
                    $total_pagu += $row['pagu_bangub'];
                    $total_realisasi += $row['realisasi_bangub'];
                    $total_sisa += $row['sisa_anggaran'];
                    
                    echo "<tr>
                        <td>".$no++."</td>
                        <td>".$row['nama_opd']."</td>
                        <td>".$nama_bulan[$row['bulan']]."</td>
                        <td align='right'>Rp ".number_format($row['pagu_bangub'],0,',','.')."</td>
                        <td align='right'>Rp ".number_format($row['realisasi_bangub'],0,',','.')."</td>
                        <td align='right'>Rp ".number_format($row['sisa_anggaran'],0,',','.')."</td>
                        <td align='right'>".number_format($row['persen_realisasi'],2)."%</td>
                        <td align='right'>".number_format($row['fisik_bangub'],2)."%</td>
                    </tr>";
                }
                
                // Tambahkan baris total
                $total_persen = $total_pagu > 0 ? ($total_realisasi / $total_pagu * 100) : 0;
                echo "<tr style='background-color: #f0f0f0; font-weight: bold;'>
                    <td colspan='3'>TOTAL</td>
                    <td align='right'>Rp ".number_format($total_pagu,0,',','.')."</td>
                    <td align='right'>Rp ".number_format($total_realisasi,0,',','.')."</td>
                    <td align='right'>Rp ".number_format($total_sisa,0,',','.')."</td>
                    <td align='right'>".number_format($total_persen,2)."%</td>
                    <td align='right'>-</td>
                </tr>";
                ?>
                </tbody>
            </table>
        </body>
        </html>
        <?php
        exit;
        break;

    case 'add':
    case 'edit':
        $data = array();
        if($act == 'edit') {
            $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
            $data = $koneksi_db->sql_fetchrow($koneksi_db->sql_query("SELECT * FROM tb_bangub WHERE id='$id'"));
        }
        ?>
        <div class="content-header">
            <div class="container-fluid">
                <h1 class="m-0"><?php echo $act == 'add' ? 'Tambah' : 'Edit' ?> Data Bantuan</h1>
            </div>
        </div>

        <section class="content">
            <div class="container-fluid">
                <div class="card">
                    <div class="card-body">
                        <form method="post" action="?page=bangub&act=save">
                            <?php if($act == 'edit') { ?>
                                <input type="hidden" name="id" value="<?php echo $data['id'] ?>">
                            <?php } ?>
                            
                            <div class="form-group">
                                <label>OPD</label>
                                <select class="form-control" name="opd_id" required>
                                    <option value="">Pilih OPD</option>
                                    <?php
                                    $opd_query = "SELECT id, nm_opd FROM siepra_opd WHERE aktif='Y' ORDER BY nm_opd ASC";
                                    $opd_result = $koneksi_db->sql_query($opd_query);
                                    while ($opd = $koneksi_db->sql_fetchrow($opd_result)) {
                                        $selected = ($act == 'edit' && $data['opd_id'] == $opd['id']) ? 'selected' : '';
                                        echo "<option value='".$opd['id']."' ".$selected.">".$opd['nm_opd']."</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Tahun</label>
                                <input type="number" class="form-control" name="tahun" value="<?php echo $act == 'edit' ? $data['tahun'] : date('Y') ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Pagu Bantuan</label>
                                <input type="number" class="form-control" name="pagu_bangub" value="<?php echo $act == 'edit' ? $data['pagu_bangub'] : '' ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Realisasi</label>
                                <input type="number" class="form-control" name="realisasi_bangub" value="<?php echo $act == 'edit' ? $data['realisasi_bangub'] : '' ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Persentase Fisik (%)</label>
                                <input type="number" class="form-control" name="fisik_bangub" value="<?php echo $act == 'edit' ? $data['fisik_bangub'] : '' ?>" required>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Simpan</button>
                                <a href="?page=bangub" class="btn btn-secondary">Kembali</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
        <?php
        break;

    case 'save':
        $opd_id = $_POST['opd_id'];
        $tahun = $_POST['tahun'];
        $pagu = $_POST['pagu_bangub'];
        $realisasi = $_POST['realisasi_bangub'];
        $fisik = $_POST['fisik_bangub'];
        
        // Hitung sisa dan persentase
        $sisa = $pagu - $realisasi;
        $persen = ($pagu > 0) ? ($realisasi / $pagu * 100) : 0;
        
        if(isset($_POST['id'])) {
            $id = $_POST['id'];
            $sql = "UPDATE tb_bangub SET 
                    opd_id='$opd_id',
                    tahun='$tahun',
                    pagu_bangub='$pagu',
                    realisasi_bangub='$realisasi',
                    sisa_anggaran='$sisa',
                    persen_realisasi='$persen',
                    fisik_bangub='$fisik'
                    WHERE id='$id'";
        } else {
            $sql = "INSERT INTO tb_bangub(opd_id,tahun,pagu_bangub,realisasi_bangub,sisa_anggaran,persen_realisasi,fisik_bangub) 
                    VALUES('$opd_id','$tahun','$pagu','$realisasi','$sisa','$persen','$fisik')";
        }
        
        $koneksi_db->sql_query($sql);
        header("Location: ?page=bangub");
        break;

    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        $koneksi_db->sql_query("DELETE FROM tb_bangub WHERE id='$id'");
        header("Location: ?page=bangub");
        break;

    default:
        $opd_filter = isset($_GET['opd_filter']) ? $_GET['opd_filter'] : '';
        $tahun_filter = isset($_GET['tahun_filter']) ? $_GET['tahun_filter'] : date('Y');
        
        $where = "WHERE b.tahun = '$tahun_filter'";
        if($opd_filter) {
            $where .= " AND b.opd_id='$opd_filter'";
        }
        
        $query = "SELECT b.*, o.nm_opd as nama_opd 
                FROM tb_bangub b
                LEFT JOIN siepra_opd o ON b.opd_id = o.id
                $where
                ORDER BY b.tahun DESC, b.opd_id ASC";
        $result = $koneksi_db->sql_query($query);
        
        // Get summary data for dashboard
        $summary_query = "SELECT 
            COUNT(DISTINCT b.opd_id) as total_opd,
            COALESCE(SUM(b.pagu_bangub), 0) as total_pagu,
            COALESCE(SUM(b.realisasi_bangub), 0) as total_realisasi,
            COALESCE(AVG(b.persen_realisasi), 0) as rata_persen,
            COALESCE(AVG(b.fisik_bangub), 0) as rata_fisik
            FROM tb_bangub b 
            WHERE b.tahun = '$tahun_filter'";
        $summary_result = $koneksi_db->sql_query($summary_query);
        $summary = $koneksi_db->sql_fetchrow($summary_result);
        
        // Fallback jika tidak ada data
        if (!$summary || $summary['total_opd'] == 0) {
            $summary = [
                'total_opd' => 0,
                'total_pagu' => 0,
                'total_realisasi' => 0,
                'rata_persen' => 0,
                'rata_fisik' => 0
            ];
        }
        
        // Get chart data for all OPD
        $chart_query = "SELECT 
            o.nm_opd,
            SUM(b.pagu_bangub) as total_pagu,
            SUM(b.realisasi_bangub) as total_realisasi,
            AVG(b.persen_realisasi) as avg_persen
            FROM siepra_opd o
            LEFT JOIN tb_bangub b ON o.id = b.opd_id AND b.tahun = '$tahun_filter'
            WHERE o.aktif = 'y'
            GROUP BY o.id, o.nm_opd
            ORDER BY total_realisasi DESC
            LIMIT 10";
        $chart_result = $koneksi_db->sql_query($chart_query);
        
        $opd_names = [];
        $realisasi_amounts = [];
        $persen_data = [];
        
        if ($chart_result && $koneksi_db->sql_numrows($chart_result) > 0) {
            while($row = $koneksi_db->sql_fetchrow($chart_result)) {
                $opd_names[] = substr($row['nm_opd'], 0, 20) . (strlen($row['nm_opd']) > 20 ? '...' : '');
                $realisasi_amounts[] = (float)$row['total_realisasi'];
                $persen_data[] = (float)$row['avg_persen'];
            }
        } else {
            // Data fallback jika tidak ada data
            $opd_names = ['Belum ada data'];
            $realisasi_amounts = [0];
            $persen_data = [0];
        }
        
        // Reset chart result for monthly trend
        $monthly_chart_query = "SELECT 
            b.bulan,
            SUM(b.realisasi_bangub) as total_realisasi,
            AVG(b.fisik_bangub) as avg_fisik
            FROM tb_bangub b
            WHERE b.tahun = '$tahun_filter'
            GROUP BY b.bulan
            ORDER BY b.bulan";
        $monthly_result = $koneksi_db->sql_query($monthly_chart_query);
        
        $monthly_realisasi = array_fill(0, 12, 0);
        $monthly_fisik = array_fill(0, 12, 0);
        
        if ($monthly_result && $koneksi_db->sql_numrows($monthly_result) > 0) {
            while($row = $koneksi_db->sql_fetchrow($monthly_result)) {
                $bulan_index = (int)$row['bulan'] - 1;
                $monthly_realisasi[$bulan_index] = (float)$row['total_realisasi'];
                $monthly_fisik[$bulan_index] = (float)$row['avg_fisik'];
            }
        }
        ?>
        
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">Dashboard Bantuan Gubernur</h1>
                    </div>
                    <div class="col-sm-6">
                        <a href="?page=bangub&act=add" class="btn btn-primary float-right ml-2">
                            <i class="fas fa-plus"></i> Tambah Data
                        </a>
                        <a href="?page=bangub&act=cetak&tahun_filter=<?php echo $tahun_filter; ?>" target="_blank" class="btn btn-success float-right">
                            <i class="fas fa-print"></i> Cetak
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <section class="content">
            <div class="container-fluid">
                <!-- Filter -->
                <div class="card mb-4">
                    <div class="card-header bg-primary">
                        <h3 class="card-title">Filter Data</h3>
                    </div>
                    <div class="card-body">
                        <form method="get">
                            <input type="hidden" name="page" value="bangub">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Tahun</label>
                                        <select class="form-control" name="tahun_filter" onchange="this.form.submit()">
                                            <?php
                                            $tahun_sekarang = date('Y');
                                            for ($i = $tahun_sekarang - 5; $i <= $tahun_sekarang + 1; $i++) {
                                                $selected = ($i == $tahun_filter) ? 'selected' : '';
                                                echo "<option value='$i' $selected>$i</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>OPD</label>
                                        <select class="form-control select2" name="opd_filter">
                                            <option value="">Semua OPD</option>
                                            <?php
                                            $opd_query = "SELECT id, nm_opd FROM siepra_opd WHERE aktif='Y' ORDER BY nm_opd ASC";
                                            $opd_result = $koneksi_db->sql_query($opd_query);
                                            while ($opd = $koneksi_db->sql_fetchrow($opd_result)) {
                                                $selected = (isset($_GET['opd_filter']) && $_GET['opd_filter'] == $opd['id']) ? 'selected' : '';
                                                echo "<option value='".$opd['id']."' ".$selected.">".$opd['nm_opd']."</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="submit" class="btn btn-primary btn-block">
                                            <i class="fas fa-search"></i> Tampilkan Data
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Summary Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="info-box bg-info">
                            <span class="info-box-icon"><i class="fas fa-building"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total OPD</span>
                                <span class="info-box-number"><?php echo number_format($summary['total_opd']); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-success">
                            <span class="info-box-icon"><i class="fas fa-money-bill-wave"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Pagu</span>
                                <span class="info-box-number">Rp <?php echo number_format($summary['total_pagu'], 0, ',', '.'); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-warning">
                            <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Realisasi</span>
                                <span class="info-box-number">Rp <?php echo number_format($summary['total_realisasi'], 0, ',', '.'); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-danger">
                            <span class="info-box-icon"><i class="fas fa-percentage"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Rata-rata Realisasi</span>
                                <span class="info-box-number"><?php echo number_format($summary['rata_persen'], 2); ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Row -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Top 10 OPD - Realisasi Tertinggi</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="reloadOpdChart()">
                                        <i class="fas fa-sync-alt"></i> Reload Chart
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="opdChart" style="height: 300px;"></canvas>
                                <div id="opdChartStatus" class="text-center mt-2" style="display: none;">
                                    <small class="text-muted">Loading chart...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Trend Realisasi Bulanan <?php echo $tahun_filter; ?></h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="reloadMonthlyChart()">
                                        <i class="fas fa-sync-alt"></i> Reload Chart
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="monthlyChart" style="height: 300px;"></canvas>
                                <div id="monthlyChartStatus" class="text-center mt-2" style="display: none;">
                                    <small class="text-muted">Loading chart...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Data Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Data Bantuan Gubernur Tahun <?php echo $tahun_filter; ?></h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel mr-2"></i>Export Excel
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="bangunanTable">
                                <thead>
                                    <tr class="text-center bg-primary">
                                        <th>No</th>
                                        <th>OPD</th>
                                        <th>Bulan</th>
                                        <th>Pagu</th>
                                        <th>Realisasi</th>
                                        <th>Sisa</th>
                                        <th>% Realisasi</th>
                                        <th>% Fisik</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php
                                $no = 1;
                                $current_opd = '';
                                $nama_bulan = array(
                                    1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
                                    5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
                                    9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
                                );
                                
                                $data_count = 0;
                                while ($row = $koneksi_db->sql_fetchrow($result)) {
                                    $data_count++;
                                    echo "<tr>";
                                    echo "<td class='text-center'>".$no++."</td>";
                                    echo "<td>";
                                    if ($row['nama_opd'] != $current_opd) {
                                        echo "<strong>".$row['nama_opd']."</strong>";
                                        echo "<br><a href='?page=bangub&act=detail_opd&opd_id=".$row['opd_id']."&tahun=".$tahun_filter."' class='btn btn-xs btn-outline-primary mt-1'>";
                                        echo "<i class='fas fa-chart-line'></i> Lihat Detail & Grafik</a>";
                                        $current_opd = $row['nama_opd'];
                                    }
                                    echo "</td>";
                                    echo "<td class='text-center'>".$nama_bulan[$row['bulan']]."</td>";
                                    echo "<td class='text-right'>Rp ".number_format($row['pagu_bangub'],0,',','.')."</td>";
                                    echo "<td class='text-right'>Rp ".number_format($row['realisasi_bangub'],0,',','.')."</td>";
                                    echo "<td class='text-right'>Rp ".number_format($row['sisa_anggaran'],0,',','.')."</td>";
                                    echo "<td class='text-center'>";
                                    echo "<div class='progress'>";
                                    echo "<div class='progress-bar bg-primary' role='progressbar' style='width: ".$row['persen_realisasi']."%'>";
                                    echo number_format($row['persen_realisasi'],2)."%";
                                    echo "</div>";
                                    echo "</div>";
                                    echo "</td>";
                                    echo "<td class='text-center'>";
                                    echo "<div class='progress'>";
                                    echo "<div class='progress-bar bg-success' role='progressbar' style='width: ".$row['fisik_bangub']."%'>";
                                    echo number_format($row['fisik_bangub'],2)."%";
                                    echo "</div>";
                                    echo "</div>";
                                    echo "</td>";
                                    echo "<td class='text-center'>";
                                    echo "<a href='?page=bangub&act=edit&id=".$row['id']."' class='btn btn-sm btn-info mb-1' title='Edit'><i class='fas fa-edit'></i></a> ";
                                    echo "<a href='?page=bangub&act=delete&id=".$row['id']."' class='btn btn-sm btn-danger mb-1' onclick='return confirm(\"Yakin hapus data?\")' title='Hapus'><i class='fas fa-trash'></i></a>";
                                    echo "</td>";
                                    echo "</tr>";
                                }
                                
                                // Jika tidak ada data
                                if ($data_count == 0) {
                                    echo "<tr>";
                                    echo "<td colspan='9' class='text-center'>";
                                    echo "<div class='alert alert-info'>";
                                    echo "<i class='fas fa-info-circle'></i> ";
                                    echo "Belum ada data bantuan gubernur untuk tahun $tahun_filter. ";
                                    echo "<a href='?page=bangub&act=add' class='btn btn-sm btn-primary ml-2'>";
                                    echo "<i class='fas fa-plus'></i> Tambah Data Sekarang</a>";
                                    echo "<br><br>";
                                    echo "<a href='test_bangub_data.php' target='_blank' class='btn btn-sm btn-warning'>";
                                    echo "<i class='fas fa-database'></i> Generate Data Sample</a>";
                                    echo "</div>";
                                    echo "</td>";
                                    echo "</tr>";
                                }
                                ?>
                                </tbody>
                            </table>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <script>
        $(document).ready(function() {
            // Initialize Select2
            $(".select2").select2();
            
            // Initialize DataTable
            $('#bangunanTable').DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "pageLength": 25,
                "order": [[1, 'asc'], [2, 'asc']],
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
            
            // Debug: Check if Chart.js is loaded
            console.log('=== BANGUB DASHBOARD DEBUG ===');
            console.log('Chart.js loaded:', typeof Chart !== 'undefined');
            console.log('jQuery loaded:', typeof $ !== 'undefined');
            console.log('OPD Names:', <?php echo json_encode($opd_names); ?>);
            console.log('Realisasi Amounts:', <?php echo json_encode($realisasi_amounts); ?>);
            console.log('Monthly Realisasi:', <?php echo json_encode($monthly_realisasi); ?>);
            console.log('Monthly Fisik:', <?php echo json_encode($monthly_fisik); ?>);
            
            // Check canvas elements
            var opdCanvas = document.getElementById('opdChart');
            var monthlyCanvas = document.getElementById('monthlyChart');
            console.log('OPD Canvas found:', opdCanvas !== null);
            console.log('Monthly Canvas found:', monthlyCanvas !== null);
            
            if (!opdCanvas) {
                console.error('Canvas #opdChart tidak ditemukan!');
            }
            if (!monthlyCanvas) {
                console.error('Canvas #monthlyChart tidak ditemukan!');
            }
            
            // Chart for Top OPD
            setTimeout(function() {
                if (typeof Chart !== 'undefined') {
                    var ctx1 = document.getElementById('opdChart');
                    console.log('OPD Chart Canvas:', ctx1);
                    
                    if (ctx1) {
                        try {
                            new Chart(ctx1, {
                                type: 'bar',
                                data: {
                                    labels: <?php echo json_encode($opd_names); ?>,
                                    datasets: [{
                                        label: 'Realisasi (Rp)',
                                        data: <?php echo json_encode($realisasi_amounts); ?>,
                                        backgroundColor: [
                                            'rgba(54, 162, 235, 0.8)',
                                            'rgba(255, 99, 132, 0.8)',
                                            'rgba(255, 205, 86, 0.8)',
                                            'rgba(75, 192, 192, 0.8)',
                                            'rgba(153, 102, 255, 0.8)',
                                            'rgba(255, 159, 64, 0.8)',
                                            'rgba(199, 199, 199, 0.8)',
                                            'rgba(83, 102, 255, 0.8)',
                                            'rgba(255, 99, 255, 0.8)',
                                            'rgba(99, 255, 132, 0.8)'
                                        ],
                                        borderColor: [
                                            'rgba(54, 162, 235, 1)',
                                            'rgba(255, 99, 132, 1)',
                                            'rgba(255, 205, 86, 1)',
                                            'rgba(75, 192, 192, 1)',
                                            'rgba(153, 102, 255, 1)',
                                            'rgba(255, 159, 64, 1)',
                                            'rgba(199, 199, 199, 1)',
                                            'rgba(83, 102, 255, 1)',
                                            'rgba(255, 99, 255, 1)',
                                            'rgba(99, 255, 132, 1)'
                                        ],
                                        borderWidth: 1
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            ticks: {
                                                callback: function(value) {
                                                    if (value >= 1000000000) {
                                                        return 'Rp ' + (value / 1000000000).toFixed(1) + 'M';
                                                    } else if (value >= 1000000) {
                                                        return 'Rp ' + (value / 1000000).toFixed(1) + 'Jt';
                                                    } else if (value >= 1000) {
                                                        return 'Rp ' + (value / 1000).toFixed(1) + 'K';
                                                    }
                                                    return 'Rp ' + value;
                                                }
                                            }
                                        }
                                    },
                                    plugins: {
                                        legend: {
                                            display: false
                                        },
                                        tooltip: {
                                            callbacks: {
                                                label: function(context) {
                                                    return 'Realisasi: Rp ' + new Intl.NumberFormat('id-ID').format(context.parsed.y);
                                                }
                                            }
                                        }
                                    }
                                }
                            });
                            console.log('OPD Chart created successfully');
                        } catch (error) {
                            console.error('Error creating OPD chart:', error);
                        }
                    }
                    
                    // Monthly trend chart
                    var ctx2 = document.getElementById('monthlyChart');
                    console.log('Monthly Chart Canvas:', ctx2);
                    
                    if (ctx2) {
                        try {
                            new Chart(ctx2, {
                                type: 'line',
                                data: {
                                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                                    datasets: [{
                                        label: 'Realisasi Keuangan (Rp)',
                                        data: <?php echo json_encode($monthly_realisasi); ?>,
                                        borderColor: '#36a2eb',
                                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                        borderWidth: 3,
                                        fill: true,
                                        tension: 0.4,
                                        yAxisID: 'y'
                                    }, {
                                        label: 'Rata-rata Fisik (%)',
                                        data: <?php echo json_encode($monthly_fisik); ?>,
                                        borderColor: '#ff6384',
                                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                                        borderWidth: 3,
                                        fill: true,
                                        tension: 0.4,
                                        yAxisID: 'y1'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    interaction: {
                                        mode: 'index',
                                        intersect: false,
                                    },
                                    scales: {
                                        y: {
                                            type: 'linear',
                                            display: true,
                                            position: 'left',
                                            title: {
                                                display: true,
                                                text: 'Realisasi (Rp)'
                                            },
                                            ticks: {
                                                callback: function(value) {
                                                    if (value >= 1000000000) {
                                                        return 'Rp ' + (value / 1000000000).toFixed(1) + 'M';
                                                    } else if (value >= 1000000) {
                                                        return 'Rp ' + (value / 1000000).toFixed(1) + 'Jt';
                                                    } else if (value >= 1000) {
                                                        return 'Rp ' + (value / 1000).toFixed(1) + 'K';
                                                    }
                                                    return 'Rp ' + value;
                                                }
                                            }
                                        },
                                        y1: {
                                            type: 'linear',
                                            display: true,
                                            position: 'right',
                                            title: {
                                                display: true,
                                                text: 'Fisik (%)'
                                            },
                                            max: 100,
                                            min: 0,
                                            ticks: {
                                                callback: function(value) {
                                                    return value + '%';
                                                }
                                            },
                                            grid: {
                                                drawOnChartArea: false
                                            }
                                        }
                                    },
                                    plugins: {
                                        legend: {
                                            position: 'top'
                                        },
                                        tooltip: {
                                            callbacks: {
                                                label: function(context) {
                                                    if (context.datasetIndex === 0) {
                                                        return 'Realisasi: Rp ' + new Intl.NumberFormat('id-ID').format(context.parsed.y);
                                                    } else {
                                                        return 'Fisik: ' + context.parsed.y + '%';
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            });
                            console.log('Monthly Chart created successfully');
                        } catch (error) {
                            console.error('Error creating monthly chart:', error);
                        }
                    }
                } else {
                    console.error('Chart.js is not loaded!');
                    alert('Chart.js tidak dimuat. Silakan refresh halaman.');
                }
            }, 1000); // Delay 1 detik untuk memastikan semua script dimuat
            
            // Fallback: Coba lagi setelah 3 detik jika chart belum muncul
            setTimeout(function() {
                var opdChart = Chart.getChart('opdChart');
                var monthlyChart = Chart.getChart('monthlyChart');
                
                if (!opdChart && typeof Chart !== 'undefined') {
                    console.log('Mencoba membuat ulang OPD chart...');
                    var ctx1 = document.getElementById('opdChart');
                    if (ctx1) {
                        // Ulangi pembuatan chart OPD
                        try {
                            new Chart(ctx1, {
                                type: 'bar',
                                data: {
                                    labels: <?php echo json_encode($opd_names); ?>,
                                    datasets: [{
                                        label: 'Realisasi (Rp)',
                                        data: <?php echo json_encode($realisasi_amounts); ?>,
                                        backgroundColor: 'rgba(54, 162, 235, 0.8)'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false
                                }
                            });
                            console.log('OPD Chart berhasil dibuat ulang');
                        } catch (error) {
                            console.error('Gagal membuat ulang OPD chart:', error);
                        }
                    }
                }
                
                if (!monthlyChart && typeof Chart !== 'undefined') {
                    console.log('Mencoba membuat ulang Monthly chart...');
                    var ctx2 = document.getElementById('monthlyChart');
                    if (ctx2) {
                        // Ulangi pembuatan chart monthly
                        try {
                            new Chart(ctx2, {
                                type: 'line',
                                data: {
                                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                                    datasets: [{
                                        label: 'Realisasi Keuangan (Rp)',
                                        data: <?php echo json_encode($monthly_realisasi); ?>,
                                        borderColor: '#36a2eb',
                                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                        borderWidth: 3,
                                        fill: true,
                                        tension: 0.4
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false
                                }
                            });
                            console.log('Monthly Chart berhasil dibuat ulang');
                        } catch (error) {
                            console.error('Gagal membuat ulang Monthly chart:', error);
                        }
                    }
                }
            }, 3000);
        });
        
        // Manual reload functions
        function reloadOpdChart() {
            $('#opdChartStatus').show().html('<small class="text-muted"><i class="fas fa-spinner fa-spin"></i> Reloading chart...</small>');
            
            var ctx = document.getElementById('opdChart');
            if (ctx) {
                // Destroy existing chart if exists
                var existingChart = Chart.getChart(ctx);
                if (existingChart) {
                    existingChart.destroy();
                }
                
                // Create new chart
                setTimeout(function() {
                    try {
                        new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: <?php echo json_encode($opd_names); ?>,
                                datasets: [{
                                    label: 'Realisasi (Rp)',
                                    data: <?php echo json_encode($realisasi_amounts); ?>,
                                    backgroundColor: [
                                        'rgba(54, 162, 235, 0.8)',
                                        'rgba(255, 99, 132, 0.8)',
                                        'rgba(255, 205, 86, 0.8)',
                                        'rgba(75, 192, 192, 0.8)',
                                        'rgba(153, 102, 255, 0.8)',
                                        'rgba(255, 159, 64, 0.8)',
                                        'rgba(199, 199, 199, 0.8)',
                                        'rgba(83, 102, 255, 0.8)',
                                        'rgba(255, 99, 255, 0.8)',
                                        'rgba(99, 255, 132, 0.8)'
                                    ]
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: { display: false }
                                }
                            }
                        });
                        $('#opdChartStatus').html('<small class="text-success"><i class="fas fa-check"></i> Chart loaded successfully</small>');
                        setTimeout(function() { $('#opdChartStatus').fadeOut(); }, 2000);
                    } catch (error) {
                        $('#opdChartStatus').html('<small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error: ' + error.message + '</small>');
                        console.error('Error reloading OPD chart:', error);
                    }
                }, 500);
            }
        }
        
        function reloadMonthlyChart() {
            $('#monthlyChartStatus').show().html('<small class="text-muted"><i class="fas fa-spinner fa-spin"></i> Reloading chart...</small>');
            
            var ctx = document.getElementById('monthlyChart');
            if (ctx) {
                // Destroy existing chart if exists
                var existingChart = Chart.getChart(ctx);
                if (existingChart) {
                    existingChart.destroy();
                }
                
                // Create new chart
                setTimeout(function() {
                    try {
                        new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                                datasets: [{
                                    label: 'Realisasi Keuangan (Rp)',
                                    data: <?php echo json_encode($monthly_realisasi); ?>,
                                    borderColor: '#36a2eb',
                                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                    borderWidth: 3,
                                    fill: true,
                                    tension: 0.4
                                }, {
                                    label: 'Rata-rata Fisik (%)',
                                    data: <?php echo json_encode($monthly_fisik); ?>,
                                    borderColor: '#ff6384',
                                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                                    borderWidth: 3,
                                    fill: true,
                                    tension: 0.4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: { position: 'top' }
                                }
                            }
                        });
                        $('#monthlyChartStatus').html('<small class="text-success"><i class="fas fa-check"></i> Chart loaded successfully</small>');
                        setTimeout(function() { $('#monthlyChartStatus').fadeOut(); }, 2000);
                    } catch (error) {
                        $('#monthlyChartStatus').html('<small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error: ' + error.message + '</small>');
                        console.error('Error reloading Monthly chart:', error);
                    }
                }, 500);
            }
        }
        
        // Export to Excel function
        function exportToExcel() {
            var wb = XLSX.utils.book_new();
            
            // Data untuk Excel
            var data = [
                ['LAPORAN BANTUAN GUBERNUR TAHUN <?php echo $tahun_filter; ?>'],
                ['BAGIAN ADMINISTRASI PEMBANGUNAN SETDA KOTA PALEMBANG'],
                [],
                ['No.', 'OPD', 'Bulan', 'Pagu', 'Realisasi', 'Sisa', '% Realisasi', '% Fisik']
            ];
            
            // Ambil data dari tabel
            $('#bangunanTable tbody tr').each(function(index) {
                var row = [];
                $(this).find('td').each(function(cellIndex) {
                    if (cellIndex === 8) return; // Skip kolom aksi
                    if (cellIndex === 6 || cellIndex === 7) { // Kolom progress bar
                        row.push($(this).text().trim());
                        return;
                    }
                    row.push($(this).text().trim());
                });
                if (row.length > 0) data.push(row);
            });
            
            var ws = XLSX.utils.aoa_to_sheet(data);
            
            // Styling
            ws['!merges'] = [
                {s: {r: 0, c: 0}, e: {r: 0, c: 7}},
                {s: {r: 1, c: 0}, e: {r: 1, c: 7}}
            ];
            
            // Column widths
            var wscols = [
                {wch: 5}, {wch: 40}, {wch: 12}, {wch: 15}, 
                {wch: 15}, {wch: 15}, {wch: 12}, {wch: 12}
            ];
            ws['!cols'] = wscols;
            
            XLSX.utils.book_append_sheet(wb, ws, 'Bantuan Gubernur');
            XLSX.writeFile(wb, 'Laporan_Bantuan_Gubernur_<?php echo $tahun_filter; ?>.xlsx');
        }
        </script>
        <?php
        break;
} // Tutup switch
?>