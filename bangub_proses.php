<?php
include '../config/koneksi.php';

// Process Add
if (isset($_POST['tambah'])) {
    $opd_id = $_POST['opd_id'];
    $tahun = $_POST['tahun'];
    $bulan = $_POST['bulan'];
    $pagu_bangub = $_POST['pagu_bangub'];
    $realisasi_bangub = $_POST['realisasi_bangub'];
    $fisik_bangub = $_POST['fisik_bangub'];
    $keterangan = $_POST['keterangan'];

    // Handle file upload
    $gambar = '';
    if (isset($_FILES['gambar']) && $_FILES['gambar']['error'] == 0) {
        $target_dir = "uploads/bangub/";
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        $file_name = time() . "_bangub." . pathinfo($_FILES["gambar"]["name"], PATHINFO_EXTENSION);
        $target_file = $target_dir . $file_name;
        
        if (move_uploaded_file($_FILES["gambar"]["tmp_name"], $target_file)) {
            $gambar = $target_file;
        }
    }

    $query = mysqli_query($koneksi, "INSERT INTO tb_bangub (opd_id, tahun, bulan, pagu_bangub, realisasi_bangub, 
        fisik_bangub, keterangan, gambar) VALUES ('$opd_id', '$tahun', '$bulan', '$pagu_bangub', 
        '$realisasi_bangub', '$fisik_bangub', '$keterangan', '$gambar')");

    if ($query) {
        echo "<script>
            alert('Data berhasil ditambahkan');
            window.location.href='bangub.php';
        </script>";
    } else {
        echo "<script>
            alert('Data gagal ditambahkan');
            window.location.href='bangub.php';
        </script>";
    }
}

// Process Edit
if (isset($_POST['edit'])) {
    $id = $_POST['id'];
    $opd_id = $_POST['opd_id'];
    $tahun = $_POST['tahun'];
    $bulan = $_POST['bulan'];
    $pagu_bangub = $_POST['pagu_bangub'];
    $realisasi_bangub = $_POST['realisasi_bangub'];
    $fisik_bangub = $_POST['fisik_bangub'];
    $keterangan = $_POST['keterangan'];
    $gambar_lama = $_POST['gambar_lama'];

    // Handle file upload
    $gambar = $gambar_lama;
    if (isset($_FILES['gambar']) && $_FILES['gambar']['error'] == 0) {
        $target_dir = "uploads/bangub/";
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        $file_name = time() . "_bangub." . pathinfo($_FILES["gambar"]["name"], PATHINFO_EXTENSION);
        $target_file = $target_dir . $file_name;
        
        if (move_uploaded_file($_FILES["gambar"]["tmp_name"], $target_file)) {
            if ($gambar_lama != '' && file_exists($gambar_lama)) {
                unlink($gambar_lama);
            }
            $gambar = $target_file;
        }
    }

    $query = mysqli_query($koneksi, "UPDATE tb_bangub SET 
        opd_id='$opd_id', 
        tahun='$tahun', 
        bulan='$bulan', 
        pagu_bangub='$pagu_bangub', 
        realisasi_bangub='$realisasi_bangub', 
        fisik_bangub='$fisik_bangub', 
        keterangan='$keterangan', 
        gambar='$gambar' 
        WHERE id='$id'");

    if ($query) {
        echo "<script>
            alert('Data berhasil diupdate');
            window.location.href='bangub.php';
        </script>";
    } else {
        echo "<script>
            alert('Data gagal diupdate');
            window.location.href='bangub.php';
        </script>";
    }
}

// Process Delete
if (isset($_GET['hapus'])) {
    $id = $_GET['hapus'];
    
    // Get image path before delete
    $query_gambar = mysqli_query($koneksi, "SELECT gambar FROM tb_bangub WHERE id='$id'");
    $data_gambar = mysqli_fetch_array($query_gambar);
    
    $query = mysqli_query($koneksi, "DELETE FROM tb_bangub WHERE id='$id'");
    
    if ($query) {
        // Delete image file if exists
        if ($data_gambar['gambar'] != '' && file_exists($data_gambar['gambar'])) {
            unlink($data_gambar['gambar']);
        }
        
        echo "<script>
            alert('Data berhasil dihapus');
            window.location.href='bangub.php';
        </script>";
    } else {
        echo "<script>
            alert('Data gagal dihapus');
            window.location.href='bangub.php';
        </script>";
    }
}