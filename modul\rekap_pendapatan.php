<?php
if (!defined('_emonevadmin_')) {
    header("Location: ../index.php");
    exit;
}

// Ambil tahun aktif
$tahun_aktif = isset($_GET['tahun']) ? (int)$_GET['tahun'] : date('Y');
$opd_id = isset($_GET['opd_id']) ? (int)$_GET['opd_id'] : 0;

// Ambil daftar OPD
$query_opd = "SELECT id, nm_opd FROM ".$namadepan."opd WHERE aktif = 'y' ORDER BY nm_opd";
$result_opd = $koneksi_db->sql_query($query_opd);

// Simpan semua OPD dalam array untuk referensi
$all_opd = array();
$result_all_opd = $koneksi_db->sql_query($query_opd);
while ($row_opd = $koneksi_db->sql_fetchrow($result_all_opd)) {
    $all_opd[$row_opd['id']] = $row_opd['nm_opd'];
}

// Query untuk data pendapatan dari tabel pendapatan_opd
$query_pendapatan = "SELECT 
    p.id,
    p.opd_id,
    p.tahun,
    p.jumlah,
    p.keterangan,
    o.nm_opd
FROM pendapatan_opd p
JOIN ".$namadepan."opd o ON p.opd_id = o.id
WHERE p.tahun = $tahun_aktif " .
($opd_id > 0 ? "AND p.opd_id = $opd_id " : "") .
"ORDER BY o.nm_opd";

$result_pendapatan = $koneksi_db->sql_query($query_pendapatan);
if (!$result_pendapatan) {
    echo "Error: Tidak dapat mengambil data pendapatan. SQL Error: " . $koneksi_db->sql_error()['message'];
}

// Hitung total pendapatan
$query_total = "SELECT 
    SUM(jumlah) as total_jumlah
FROM pendapatan_opd
WHERE tahun = $tahun_aktif " .
($opd_id > 0 ? "AND opd_id = $opd_id " : "");

$result_total = $koneksi_db->sql_query($query_total);
$total = $koneksi_db->sql_fetchrow($result_total);

// Set default values if no data found
if (!$total) {
    $total = array(
        'total_jumlah' => 0
    );
}

// Query untuk data grafik per tahun (trend 5 tahun terakhir)
$chart_query_yearly = "SELECT 
    tahun,
    SUM(jumlah) as total_jumlah
FROM pendapatan_opd " .
($opd_id > 0 ? "WHERE opd_id = $opd_id " : "") .
"GROUP BY tahun
ORDER BY tahun DESC
LIMIT 5";

$chart_result_yearly = $koneksi_db->sql_query($chart_query_yearly);
$tahun_data = [];
$jumlah_data = [];

while($row = $koneksi_db->sql_fetchrow($chart_result_yearly)) {
    $tahun_data[] = $row['tahun'];
    $jumlah_data[] = (float)$row['total_jumlah'];
}

// Reverse arrays to show chronological order
$tahun_data = array_reverse($tahun_data);
$jumlah_data = array_reverse($jumlah_data);

// Query untuk data grafik per OPD (top 10)
$chart_query_opd = "SELECT 
    o.id,
    o.nm_opd,
    SUM(p.jumlah) as total_jumlah
FROM pendapatan_opd p
JOIN ".$namadepan."opd o ON p.opd_id = o.id
WHERE p.tahun = '$tahun_aktif'
GROUP BY o.id, o.nm_opd
HAVING total_jumlah > 0
ORDER BY total_jumlah DESC
LIMIT 10";

// Tambahkan debug untuk melihat hasil query
$chart_result_opd = $koneksi_db->sql_query($chart_query_opd);
if (!$chart_result_opd) {
    echo "Error query OPD chart: " . $koneksi_db->sql_error()['message'];
}

// Cek apakah ada data yang diambil
$opd_labels = [];
$opd_jumlah = [];
$data_count = 0;

while($row = $koneksi_db->sql_fetchrow($chart_result_opd)) {
    $data_count++;
    $opd_labels[] = $row['nm_opd'];
    $opd_jumlah[] = (float)$row['total_jumlah'];
}

// Tambahkan fallback jika tidak ada data
if ($data_count == 0) {
    $opd_labels = ['Tidak ada data'];
    $opd_jumlah = [0];
}
?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Rekap Dana Pendapatan</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="?page=dashboard">Beranda</a></li>
                    <li class="breadcrumb-item active">Rekap Pendapatan</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Filter -->
        <div class="card mb-4">
            <div class="card-header bg-primary">
                <h3 class="card-title">Filter Data</h3>
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <input type="hidden" name="page" value="rekap_pendapatan">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="form-group">
                                <label>Pilih OPD</label>
                                <select name="opd_id" class="form-control">
                                    <option value="0">-- Semua OPD --</option>
                                    <?php
                                    $result_opd = $koneksi_db->sql_query($query_opd);
                                    while ($row_opd = $koneksi_db->sql_fetchrow($result_opd)) {
                                        $selected = ($row_opd['id'] == $opd_id) ? 'selected' : '';
                                        echo "<option value='{$row_opd['id']}' $selected>{$row_opd['nm_opd']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-group">
                                <label>Pilih Tahun</label>
                                <select name="tahun" class="form-control">
                                    <?php
                                    $tahun_sekarang = date('Y');
                                    for ($i = $tahun_sekarang - 5; $i <= $tahun_sekarang + 1; $i++) {
                                        $selected = ($i == $tahun_aktif) ? 'selected' : '';
                                        echo "<option value='$i' $selected>$i</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search mr-2"></i> Tampilkan
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Info Boxes -->
        <div class="row">
            <div class="col-md-12">
                <div class="info-box bg-success">
                    <span class="info-box-icon"><i class="fas fa-money-bill-wave"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Pendapatan</span>
                        <span class="info-box-number">Rp <?php echo number_format($total['total_jumlah'], 0, ',', '.'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row">
            <!-- Yearly Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Grafik Trend Pendapatan 5 Tahun Terakhir</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="pendapatanChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>

            <!-- OPD Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">10 OPD Teratas - Pendapatan <?php echo $tahun_aktif; ?></h3>
                    </div>
                    <div class="card-body">
                        <canvas id="opdChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="card">
            <div class="card-header bg-primary">
                <h3 class="card-title">Data Pendapatan</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-sm btn-success" onclick="exportTableToExcel('tablePendapatan', 'Rekap_Pendapatan_<?php echo $tahun_aktif; ?>')">
                        <i class="fas fa-file-excel mr-1"></i> Export Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-default" onclick="printTable('tablePendapatan', 'Rekap Pendapatan <?php echo $tahun_aktif; ?>')">
                        <i class="fas fa-print mr-1"></i> Cetak
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="tablePendapatan">
                        <thead>
                            <tr class="text-center bg-primary">
                                <th>No.</th>
                                <th>OPD</th>
                                <th>Tahun</th>
                                <th>Jumlah</th>
                                <th>Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            $opd_with_data = array();
                            
                            // Reset the result pointer
                            $result_pendapatan = $koneksi_db->sql_query($query_pendapatan);
                            
                            while ($row = $koneksi_db->sql_fetchrow($result_pendapatan)) {
                                $opd_with_data[$row['opd_id']] = true;
                                echo "<tr>";
                                echo "<td class='text-center'>{$no}</td>";
                                echo "<td>{$row['nm_opd']}</td>";
                                echo "<td class='text-center'>{$row['tahun']}</td>";
                                echo "<td class='text-right'>" . number_format($row['jumlah'], 0, ',', '.') . "</td>";
                                echo "<td>{$row['keterangan']}</td>";
                                echo "</tr>";
                                $no++;
                            }

                            // Tampilkan OPD yang tidak memiliki data jika tidak ada filter OPD
                            if ($opd_id == 0) {
                                foreach ($all_opd as $id => $nama) {
                                    if (!isset($opd_with_data[$id])) {
                                        echo "<tr class='table-warning'>";
                                        echo "<td class='text-center'>{$no}</td>";
                                        echo "<td>{$nama}</td>";
                                        echo "<td class='text-center'>{$tahun_aktif}</td>";
                                        echo "<td class='text-right'>0</td>";
                                        echo "<td>Data belum tersedia</td>";
                                        echo "</tr>";
                                        $no++;
                                    }
                                }
                            }

                            // Check if there are no results at all
                            if ($no == 1) {
                                echo "<tr><td colspan='5' class='text-center'>Tidak ada data yang tersedia</td></tr>";
                            }
                            ?>
                        </tbody>
                        <tfoot>
                            <tr class="bg-light">
                                <th colspan="3" class="text-right">Total:</th>
                                <th class="text-right"><?php echo number_format($total['total_jumlah'], 0, ',', '.'); ?></th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript untuk export dan print -->
<script>
function exportTableToExcel(tableID, filename = '') {
    var downloadLink;
    var dataType = 'application/vnd.ms-excel';
    var tableSelect = document.getElementById(tableID);
    var tableHTML = tableSelect.outerHTML.replace(/ /g, '%20');
    
    // Nama file
    filename = filename ? filename + '.xls' : 'excel_data.xls';
    
    // Buat link download
    downloadLink = document.createElement("a");
    
    document.body.appendChild(downloadLink);
    
    if(navigator.msSaveOrOpenBlob) {
        var blob = new Blob(['\ufeff', tableHTML], {
            type: dataType
        });
        navigator.msSaveOrOpenBlob(blob, filename);
    } else {
        // Buat link yang dihidden
        downloadLink.href = 'data:' + dataType + ', ' + tableHTML;
        downloadLink.download = filename;
        // Klik link
        downloadLink.click();
    }
}

function printTable(tableId, title) {
    var printContents = document.getElementById(tableId).outerHTML;
    var originalContents = document.body.innerHTML;
    
    document.body.innerHTML = '<h1 style="text-align: center;">' + title + '</h1>' + printContents;
    
    window.print();
    
    document.body.innerHTML = originalContents;
}

$(document).ready(function() {
    // Chart untuk pendapatan tahunan
    var ctxYearly = document.getElementById('pendapatanChart');
    if (ctxYearly) {
        new Chart(ctxYearly, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($tahun_data); ?>,
                datasets: [{
                    label: 'Total Pendapatan',
                    data: <?php echo json_encode($jumlah_data); ?>,
                    backgroundColor: 'rgba(75, 192, 192, 0.4)',
                    borderColor: 'rgb(75, 192, 192)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Nilai (Rp)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString('id-ID');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rp ' + context.parsed.y.toLocaleString('id-ID');
                                return label;
                            }
                        }
                    }
                }
            }
        });
    }

    // Chart untuk OPD - perbaiki tipe chart dari horizontalBar ke bar dengan indexAxis: 'y'
    var ctxOpd = document.getElementById('opdChart');
    if (ctxOpd) {
        new Chart(ctxOpd, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($opd_labels); ?>,
                datasets: [{
                    label: 'Pendapatan',
                    data: <?php echo json_encode($opd_jumlah); ?>,
                    backgroundColor: 'rgba(75, 192, 192, 0.4)',
                    borderColor: 'rgb(75, 192, 192)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y', // Untuk membuat bar horizontal di Chart.js v3+
                scales: {
                    x: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Rp ' + context.raw.toLocaleString('id-ID');
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
