
<?php
if (!defined('_emonevadmin_')) {
    header("Location: index.php");
    exit;
}

// Ambil tahun aktif
$tahun_aktif = isset($_GET['tahun']) ? (int)$_GET['tahun'] : date('Y');
$opd_id = isset($_GET['opd_id']) ? (int)$_GET['opd_id'] : 0;

// Ambil daftar OPD
$query_opd = "SELECT id, nm_opd FROM siepra_opd WHERE aktif = 'y' ORDER BY nm_opd";
$result_opd = $koneksi_db->sql_query($query_opd);

// Array nama bulan
$nama_bulan = array(
    1 => 'Januari',
    2 => 'Februari',
    3 => 'Maret',
    4 => 'April',
    5 => 'Mei',
    6 => 'Juni',
    7 => 'Juli',
    8 => 'Agustus',
    9 => 'September',
    10 => 'Oktober',
    11 => 'November',
    12 => 'Desember'
);

// Ambil semua OPD terlebih dahulu
$query_all_opd = "SELECT id, nm_opd FROM siepra_opd WHERE aktif = 'y' ORDER BY nm_opd";
$result_all_opd = $koneksi_db->sql_query($query_all_opd);
$all_opd = array();
while ($row_opd = $koneksi_db->sql_fetchrow($result_all_opd)) {
    $all_opd[$row_opd['id']] = $row_opd['nm_opd'];
}

// Tambahkan query untuk mengambil data dari tabel pagu_realisasi
$query_pagu_realisasi = "SELECT
    p.opd_id,
    o.nm_opd,
    p.tahun,
    SUM(p.pagu) as total_pagu,
    SUM(p.realisasi) as total_realisasi,
    (SUM(p.pagu) - SUM(p.realisasi)) as total_sisa,
    CASE
        WHEN SUM(p.pagu) > 0 THEN ROUND((SUM(p.realisasi) / SUM(p.pagu) * 100), 2)
        ELSE 0
    END as persen_realisasi,
    AVG(p.fisik) as rata_fisik
FROM pagu_realisasi p
JOIN siepra_opd o ON p.opd_id = o.id
WHERE p.tahun = $tahun_aktif " .
($opd_id > 0 ? "AND p.opd_id = $opd_id " : "") .
"GROUP BY p.opd_id, o.nm_opd, p.tahun
ORDER BY o.nm_opd";

// Tambahkan debug untuk melihat query
echo "<!-- Query pagu_realisasi: " . $query_pagu_realisasi . " -->";

$result_pagu_realisasi = $koneksi_db->sql_query($query_pagu_realisasi);
if (!$result_pagu_realisasi) {
    echo "<!-- Error query pagu -->";
}

// Cek jumlah data
$pagu_count = $koneksi_db->sql_numrows($result_pagu_realisasi);
echo "<!-- Jumlah data pagu: $pagu_count -->";

// Tambahkan debug untuk melihat data yang diambil
if ($pagu_count > 0) {
    $debug_data = [];
    // Simpan posisi pointer result set
    $koneksi_db->sql_freeresult($result_pagu_realisasi);
    
    // Jalankan query lagi
    $result_pagu_realisasi = $koneksi_db->sql_query($query_pagu_realisasi);
    while ($row = $koneksi_db->sql_fetchrow($result_pagu_realisasi)) {
        $debug_data[] = "OPD: {$row['nm_opd']}, Pagu: {$row['total_pagu']}";
    }
    echo "<!-- Data pagu: " . implode(" | ", $debug_data) . " -->";
    
    // Reset result set untuk digunakan lagi nanti
    $koneksi_db->sql_freeresult($result_pagu_realisasi);
    $result_pagu_realisasi = $koneksi_db->sql_query($query_pagu_realisasi);
}

// Ambil data dari tabel tb_bangub
$query_bangub = "SELECT
    b.*,
    o.nm_opd
FROM tb_bangub b
JOIN siepra_opd o ON b.opd_id = o.id
WHERE b.tahun = $tahun_aktif " .
($opd_id > 0 ? "AND b.opd_id = $opd_id " : "") .
"ORDER BY o.nm_opd, b.bulan";

$result_bangub = $koneksi_db->sql_query($query_bangub);

// Ambil data dari tabel fisikmj
$query_fisik = "SELECT
    f.*,
    o.nm_opd
FROM fisikmj f
JOIN siepra_opd o ON f.opd_id = o.id
WHERE f.tahun = $tahun_aktif " .
($opd_id > 0 ? "AND f.opd_id = $opd_id " : "") .
"ORDER BY o.nm_opd, f.kode_rekening";

// Simpan OPD yang memiliki data
$opd_with_bangub_data = array();
$opd_with_fisik_data = array();

$result_fisik = $koneksi_db->sql_query($query_fisik);
if (!$result_fisik) {
    // Perbaiki cara menampilkan error
    echo "<!-- Error query fisik -->";
}

// Cek jumlah data
$fisik_count = $koneksi_db->sql_numrows($result_fisik);
echo "<!-- Jumlah data fisik: $fisik_count -->";

// Hitung total untuk tb_bangub
$query_total_bangub = "SELECT
    SUM(pagu_bangub) as total_pagu,
    SUM(realisasi_bangub) as total_realisasi,
    SUM(sisa_anggaran) as total_sisa,
    CASE
        WHEN SUM(pagu_bangub) > 0 THEN ROUND((SUM(realisasi_bangub) / SUM(pagu_bangub) * 100), 2)
        ELSE 0
    END as persen_realisasi,
    AVG(fisik_bangub) as rata_fisik
FROM tb_bangub
WHERE tahun = $tahun_aktif " .
($opd_id > 0 ? "AND opd_id = $opd_id" : "");

$result_total_bangub = $koneksi_db->sql_query($query_total_bangub);
$total_bangub = $koneksi_db->sql_fetchrow($result_total_bangub);

// Hitung total untuk fisikmj
$query_total_fisik = "SELECT
    SUM(nilai_dpa) as total_dpa,
    SUM(nilai_kontrak) as total_kontrak,
    SUM(realisasi) as total_realisasi,
    SUM(sisa_realisasi) as total_sisa,
    CASE
        WHEN SUM(nilai_dpa) > 0 THEN ROUND((SUM(realisasi) / SUM(nilai_dpa) * 100), 2)
        ELSE 0
    END as persen_realisasi
FROM fisikmj
WHERE tahun = $tahun_aktif " .
($opd_id > 0 ? "AND opd_id = $opd_id" : "");

$result_total_fisik = $koneksi_db->sql_query($query_total_fisik);
$total_fisik = $koneksi_db->sql_fetchrow($result_total_fisik);

// Hitung total untuk pagu_realisasi
$query_total_pagu = "SELECT
    SUM(pagu) as total_pagu,
    SUM(realisasi) as total_realisasi,
    SUM(sisa) as total_sisa,
    CASE
        WHEN SUM(pagu) > 0 THEN ROUND((SUM(realisasi) / SUM(pagu) * 100), 2)
        ELSE 0
    END as persen_realisasi,
    AVG(fisik) as rata_fisik
FROM pagu_realisasi
WHERE tahun = $tahun_aktif " .
($opd_id > 0 ? "AND opd_id = $opd_id" : "");

$result_total_pagu = $koneksi_db->sql_query($query_total_pagu);
$total_pagu = $koneksi_db->sql_fetchrow($result_total_pagu);

// Cek struktur tabel pagu_realisasi
$check_table = $koneksi_db->sql_query("SHOW TABLES LIKE 'pagu_realisasi'");
$table_exists = $koneksi_db->sql_numrows($check_table) > 0;
echo "<!-- Tabel pagu_realisasi " . ($table_exists ? "ada" : "tidak ada") . " -->";

if ($table_exists) {
    // Cek struktur tabel
    $check_columns = $koneksi_db->sql_query("DESCRIBE pagu_realisasi");
    $columns = [];
    while ($row = $koneksi_db->sql_fetchrow($check_columns)) {
        $columns[] = $row['Field'];
    }
    echo "<!-- Kolom tabel pagu_realisasi: " . implode(", ", $columns) . " -->";
    
    // Periksa apakah kolom sisa ada
    $has_sisa_column = in_array('sisa', $columns);
    
    // Sesuaikan query berdasarkan keberadaan kolom sisa
    if (!$has_sisa_column) {
        $query_pagu_realisasi = "SELECT
            p.opd_id,
            o.nm_opd,
            p.tahun,
            SUM(p.pagu) as total_pagu,
            SUM(p.realisasi) as total_realisasi,
            SUM(p.pagu - p.realisasi) as total_sisa,
            CASE
                WHEN SUM(p.pagu) > 0 THEN ROUND((SUM(p.realisasi) / SUM(p.pagu) * 100), 2)
                ELSE 0
            END as persen_realisasi,
            AVG(p.fisik) as rata_fisik
        FROM pagu_realisasi p
        JOIN siepra_opd o ON p.opd_id = o.id
        WHERE p.tahun = $tahun_aktif " .
        ($opd_id > 0 ? "AND p.opd_id = $opd_id " : "") .
        "GROUP BY p.opd_id, o.nm_opd, p.tahun
        ORDER BY o.nm_opd";
    }
    
    // Query sederhana untuk mengecek data
    $simple_query = "SELECT COUNT(*) as total FROM pagu_realisasi WHERE tahun = $tahun_aktif";
    $simple_result = $koneksi_db->sql_query($simple_query);
    $simple_data = $koneksi_db->sql_fetchrow($simple_result);
    echo "<!-- Total data pagu_realisasi untuk tahun $tahun_aktif: " . $simple_data['total'] . " -->";
}
?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Data Anggaran dan Realisasi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="?page=dashboard">Beranda</a></li>
                    <li class="breadcrumb-item active">Data Anggaran</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Filter -->
        <div class="card mb-4">
            <div class="card-header bg-primary">
                <h3 class="card-title">Filter Data</h3>
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <input type="hidden" name="page" value="data_anggaran">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="form-group">
                                <label>Pilih OPD</label>
                                <select name="opd_id" class="form-control">
                                    <option value="0">-- Semua OPD --</option>
                                    <?php
                                    while ($row_opd = $koneksi_db->sql_fetchrow($result_opd)) {
                                        $selected = ($row_opd['id'] == $opd_id) ? 'selected' : '';
                                        echo "<option value='{$row_opd['id']}' $selected>{$row_opd['nm_opd']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-group">
                                <label>Pilih Tahun</label>
                                <select name="tahun" class="form-control">
                                    <?php
                                    $tahun_sekarang = date('Y');
                                    for ($i = $tahun_sekarang - 5; $i <= $tahun_sekarang + 1; $i++) {
                                        $selected = ($i == $tahun_aktif) ? 'selected' : '';
                                        echo "<option value='$i' $selected>$i</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search mr-2"></i> Tampilkan
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Ringkasan -->
        <div class="row">
            <div class="col-md-6">
                <div class="info-box bg-info">
                    <span class="info-box-icon"><i class="fas fa-building"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Anggaran Bangunan</span>
                        <span class="info-box-number">Rp <?php echo number_format($total_bangub['total_pagu'], 0, ',', '.'); ?></span>
                        <div class="progress">
                            <div class="progress-bar" style="width: <?php echo $total_bangub['persen_realisasi']; ?>%"></div>
                        </div>
                        <span class="progress-description">
                            Realisasi: <?php echo number_format($total_bangub['persen_realisasi'], 2); ?>% (Rp <?php echo number_format($total_bangub['total_realisasi'], 0, ',', '.'); ?>)
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-box bg-success">
                    <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Anggaran Fisik</span>
                        <span class="info-box-number">Rp <?php echo number_format($total_fisik['total_dpa'], 0, ',', '.'); ?></span>
                        <div class="progress">
                            <div class="progress-bar" style="width: <?php echo $total_fisik['persen_realisasi']; ?>%"></div>
                        </div>
                        <span class="progress-description">
                            Realisasi: <?php echo number_format($total_fisik['persen_realisasi'], 2); ?>% (Rp <?php echo number_format($total_fisik['total_realisasi'], 0, ',', '.'); ?>)
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="card">
            <div class="card-header p-2">
                <ul class="nav nav-pills">
                    <li class="nav-item">
                        <a class="nav-link active" href="#tab_bangub" data-toggle="tab">Data Bangunan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#tab_fisik" data-toggle="tab">Data Fisik</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#tab_pagu" data-toggle="tab">Data Anggaran</a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- Tab Bangunan -->
                    <div class="tab-pane active" id="tab_bangub">
                        <div class="mb-3">
                            <button type="button" class="btn btn-success btn-sm" onclick="exportTableToExcel('tabelBangunan', 'Data_Bangunan_<?php echo $tahun_aktif; ?>')">
                                <i class="fas fa-file-excel mr-2"></i>Export Excel
                            </button>
                            <button type="button" class="btn btn-primary btn-sm ml-2" onclick="printTable('tabelBangunan', 'Data Bangunan <?php echo $tahun_aktif; ?>')">
                                <i class="fas fa-print mr-2"></i>Cetak
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="tabelBangunan">
                                <thead>
                                    <tr class="text-center bg-primary">
                                        <th>No.</th>
                                        <th>OPD</th>
                                        <th>Tahun</th>
                                        <th>Bulan</th>
                                        <th>Pagu</th>
                                        <th>Realisasi</th>
                                        <th>Sisa Anggaran</th>
                                        <th>Realisasi (%)</th>
                                        <th>Fisik (%)</th>
                                        <th>Keterangan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    // Simpan OPD yang memiliki data bangunan
                                    $opd_with_data = array();

                                    // Simpan data bangunan untuk ditampilkan
                                    $bangub_data = array();
                                    while ($row = $koneksi_db->sql_fetchrow($result_bangub)) {
                                        $opd_with_data[$row['opd_id']] = true;
                                        $bangub_data[] = $row;
                                    }

                                    // Tampilkan data bangunan
                                    foreach ($bangub_data as $row) {
                                        echo "<tr>";
                                        echo "<td class='text-center'>{$no}</td>";
                                        echo "<td>{$row['nm_opd']}</td>";
                                        echo "<td class='text-center'>{$row['tahun']}</td>";
                                        echo "<td class='text-center'>{$nama_bulan[$row['bulan']]}</td>";
                                        echo "<td class='text-right'>" . number_format($row['pagu_bangub'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['realisasi_bangub'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['sisa_anggaran'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-primary' role='progressbar' style='width: {$row['persen_realisasi']}%'>";
                                        echo number_format($row['persen_realisasi'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-success' role='progressbar' style='width: {$row['fisik_bangub']}%'>";
                                        echo number_format($row['fisik_bangub'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "<td>{$row['keterangan']}</td>";
                                        echo "</tr>";
                                        $no++;
                                    }

                                    // Tampilkan OPD yang tidak memiliki data
                                    if ($opd_id == 0) { // Hanya tampilkan semua OPD jika tidak ada filter OPD
                                        foreach ($all_opd as $id => $nama) {
                                            if (!isset($opd_with_data[$id])) {
                                                echo "<tr class='table-warning'>";
                                                echo "<td class='text-center'>{$no}</td>";
                                                echo "<td>{$nama}</td>";
                                                echo "<td class='text-center'>{$tahun_aktif}</td>";
                                                echo "<td class='text-center'>-</td>";
                                                echo "<td class='text-right'>0</td>";
                                                echo "<td class='text-right'>0</td>";
                                                echo "<td class='text-right'>0</td>";
                                                echo "<td class='text-center'>0%</td>";
                                                echo "<td class='text-center'>0%</td>";
                                                echo "<td><span class='badge badge-warning'>Belum Ada Data</span></td>";
                                                echo "</tr>";
                                                $no++;
                                            }
                                        }
                                    }
                                    ?>
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light font-weight-bold">
                                        <td colspan="4" class="text-right">Total:</td>
                                        <td class="text-right"><?php echo number_format($total_bangub['total_pagu'], 0, ',', '.'); ?></td>
                                        <td class="text-right"><?php echo number_format($total_bangub['total_realisasi'], 0, ',', '.'); ?></td>
                                        <td class="text-right"><?php echo number_format($total_bangub['total_sisa'], 0, ',', '.'); ?></td>
                                        <td class="text-center"><?php echo number_format($total_bangub['persen_realisasi'], 2); ?>%</td>
                                        <td class="text-center"><?php echo number_format($total_bangub['rata_fisik'], 2); ?>%</td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>

                    <!-- Tab Fisik -->
                    <div class="tab-pane" id="tab_fisik">
                        <div class="mb-3">
                            <button type="button" class="btn btn-success btn-sm" onclick="exportTableToExcel('tabelFisik', 'Data_Fisik_<?php echo $tahun_aktif; ?>')">
                                <i class="fas fa-file-excel mr-2"></i>Export Excel
                            </button>
                            <button type="button" class="btn btn-primary btn-sm ml-2" onclick="printTable('tabelFisik', 'Data Fisik <?php echo $tahun_aktif; ?>')">
                                <i class="fas fa-print mr-2"></i>Cetak
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="tabelFisik">
                                <thead>
                                    <tr class="text-center bg-primary">
                                        <th>No.</th>
                                        <th>OPD</th>
                                        <th>Kode Rekening</th>
                                        <th>Tahun</th>
                                        <th>Uraian</th>
                                        <th>Nilai DPA</th>
                                        <th>Nilai Kontrak</th>
                                        <th>Realisasi</th>
                                        <th>Sisa</th>
                                        <th>Realisasi (%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    // Simpan OPD yang memiliki data fisik
                                    $opd_with_data = array();

                                    // Simpan data fisik untuk ditampilkan
                                    $fisik_data = array();
                                    while ($row = $koneksi_db->sql_fetchrow($result_fisik)) {
                                        $opd_with_data[$row['opd_id']] = true;
                                        $fisik_data[] = $row;
                                    }

                                    // Tampilkan data fisik
                                    foreach ($fisik_data as $row) {
                                        $persen_realisasi = $row['nilai_dpa'] > 0 ? round(($row['realisasi'] / $row['nilai_dpa'] * 100), 2) : 0;

                                        echo "<tr>";
                                        echo "<td class='text-center'>{$no}</td>";
                                        echo "<td>{$row['nm_opd']}</td>";
                                        echo "<td>{$row['kode_rekening']}</td>";
                                        echo "<td class='text-center'>{$row['tahun']}</td>";
                                        echo "<td>{$row['uraian']}</td>";
                                        echo "<td class='text-right'>" . number_format($row['nilai_dpa'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['nilai_kontrak'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['realisasi'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['sisa_realisasi'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-success' role='progressbar' style='width: {$persen_realisasi}%'>";
                                        echo number_format($persen_realisasi, 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "</tr>";
                                        $no++;
                                    }

                                    // Tampilkan OPD yang tidak memiliki data
                                    if ($opd_id == 0) { // Hanya tampilkan semua OPD jika tidak ada filter OPD
                                        foreach ($all_opd as $id => $nama) {
                                            if (!isset($opd_with_data[$id])) {
                                                echo "<tr class='table-warning'>";
                                                echo "<td class='text-center'>{$no}</td>";
                                                echo "<td>{$nama}</td>";
                                                echo "<td>-</td>";
                                                echo "<td class='text-center'>{$tahun_aktif}</td>";
                                                echo "<td>-</td>";
                                                echo "<td class='text-right'>0</td>";
                                                echo "<td class='text-right'>0</td>";
                                                echo "<td class='text-right'>0</td>";
                                                echo "<td class='text-right'>0</td>";
                                                echo "<td><span class='badge badge-warning'>Belum Ada Data</span></td>";
                                                echo "</tr>";
                                                $no++;
                                            }
                                        }
                                    }
                                    ?>
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light font-weight-bold">
                                        <td colspan="5" class="text-right">Total:</td>
                                        <td class="text-right"><?php echo number_format($total_fisik['total_dpa'], 0, ',', '.'); ?></td>
                                        <td class="text-right"><?php echo number_format($total_fisik['total_kontrak'], 0, ',', '.'); ?></td>
                                        <td class="text-right"><?php echo number_format($total_fisik['total_realisasi'], 0, ',', '.'); ?></td>
                                        <td class="text-right"><?php echo number_format($total_fisik['total_sisa'], 0, ',', '.'); ?></td>
                                        <td class="text-center"><?php echo number_format($total_fisik['persen_realisasi'], 2); ?>%</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>

                    <!-- Tab Anggaran dari pagu_realisasi -->
                    <div class="tab-pane" id="tab_pagu">
                        <div class="mb-3">
                            <button type="button" class="btn btn-success btn-sm" onclick="exportTableToExcel('tabelAnggaran', 'Data_Anggaran_<?php echo $tahun_aktif; ?>')">
                                <i class="fas fa-file-excel mr-2"></i>Export Excel
                            </button>
                            <button type="button" class="btn btn-primary btn-sm ml-2" onclick="printTable('tabelAnggaran', 'Data Anggaran <?php echo $tahun_aktif; ?>')">
                                <i class="fas fa-print mr-2"></i>Cetak
                            </button>
                        </div>

                        <?php
                        // Debug: Cek apakah tabel pagu_realisasi ada
                        $check_table = $koneksi_db->sql_query("SHOW TABLES LIKE 'pagu_realisasi'");
                        $table_exists = $koneksi_db->sql_numrows($check_table) > 0;

                        if (!$table_exists) {
                            echo '<div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Tabel pagu_realisasi tidak ditemukan dalam database.
                                  </div>';
                        } else {
                            // Cek apakah ada data
                            $check_data = $koneksi_db->sql_query("SELECT COUNT(*) as total FROM pagu_realisasi WHERE tahun = $tahun_aktif");
                            $data_count = $koneksi_db->sql_fetchrow($check_data);

                            if ($data_count['total'] == 0) {
                                echo '<div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        Tidak ada data anggaran untuk tahun ' . $tahun_aktif . '.
                                      </div>';
                            }
                        }
                        ?>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="tabelAnggaran">
                                <thead>
                                    <tr class="text-center bg-primary">
                                        <th>No.</th>
                                        <th>OPD</th>
                                        <th>Tahun</th>
                                        <th>Pagu Anggaran</th>
                                        <th>Realisasi</th>
                                        <th>Sisa Anggaran</th>
                                        <th>Realisasi (%)</th>
                                        <th>Fisik (%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    $total_pagu_all = 0;
                                    $total_realisasi_all = 0;
                                    $total_sisa_all = 0;
                                    $total_fisik_all = 0;
                                    $count_opd = 0;

                                    try {
                                        // Query yang disederhanakan dan diperbaiki
                                        $query_pagu_data = "SELECT
                                            p.opd_id,
                                            o.nm_opd,
                                            p.tahun,
                                            SUM(p.pagu) as total_pagu,
                                            SUM(p.realisasi) as total_realisasi,
                                            (SUM(p.pagu) - SUM(p.realisasi)) as total_sisa,
                                            CASE
                                                WHEN SUM(p.pagu) > 0 THEN ROUND((SUM(p.realisasi) / SUM(p.pagu) * 100), 2)
                                                ELSE 0
                                            END as persen_realisasi,
                                            AVG(p.fisik) as rata_fisik
                                        FROM pagu_realisasi p
                                        JOIN siepra_opd o ON p.opd_id = o.id
                                        WHERE p.tahun = $tahun_aktif";

                                        if ($opd_id > 0) {
                                            $query_pagu_data .= " AND p.opd_id = $opd_id";
                                        }

                                        $query_pagu_data .= " GROUP BY p.opd_id, o.nm_opd, p.tahun ORDER BY o.nm_opd";

                                        $result_pagu_data = $koneksi_db->sql_query($query_pagu_data);

                                        if ($result_pagu_data && $koneksi_db->sql_numrows($result_pagu_data) > 0) {
                                            while ($row = $koneksi_db->sql_fetchrow($result_pagu_data)) {
                                                $total_pagu_all += $row['total_pagu'];
                                                $total_realisasi_all += $row['total_realisasi'];
                                                $total_sisa_all += $row['total_sisa'];
                                                $total_fisik_all += $row['rata_fisik'];
                                                $count_opd++;

                                                echo "<tr>";
                                                echo "<td class='text-center'>{$no}</td>";
                                                echo "<td>{$row['nm_opd']}</td>";
                                                echo "<td class='text-center'>{$row['tahun']}</td>";
                                                echo "<td class='text-right'>Rp " . number_format($row['total_pagu'], 0, ',', '.') . "</td>";
                                                echo "<td class='text-right'>Rp " . number_format($row['total_realisasi'], 0, ',', '.') . "</td>";
                                                echo "<td class='text-right'>Rp " . number_format($row['total_sisa'], 0, ',', '.') . "</td>";
                                                echo "<td class='text-center'>";
                                                echo "<div class='progress' style='height: 20px;'>";
                                                echo "<div class='progress-bar bg-success' role='progressbar' style='width: {$row['persen_realisasi']}%'>";
                                                echo number_format($row['persen_realisasi'], 2) . "%";
                                                echo "</div>";
                                                echo "</div>";
                                                echo "</td>";
                                                echo "<td class='text-center'>" . number_format($row['rata_fisik'], 2) . "%</td>";
                                                echo "</tr>";
                                                $no++;
                                            }
                                        } else {
                                            echo "<tr>";
                                            echo "<td colspan='8' class='text-center text-muted'>Tidak ada data untuk ditampilkan</td>";
                                            echo "</tr>";
                                        }
                                    } catch (Exception $e) {
                                        echo "<tr>";
                                        echo "<td colspan='8' class='text-center text-danger'>Error: " . $e->getMessage() . "</td>";
                                        echo "</tr>";
                                    }
                                    ?>
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light font-weight-bold">
                                        <td colspan="3" class="text-right">Total:</td>
                                        <td class="text-right">Rp <?php echo number_format($total_pagu_all, 0, ',', '.'); ?></td>
                                        <td class="text-right">Rp <?php echo number_format($total_realisasi_all, 0, ',', '.'); ?></td>
                                        <td class="text-right">Rp <?php echo number_format($total_sisa_all, 0, ',', '.'); ?></td>
                                        <td class="text-center">
                                            <?php
                                            $persen_total = $total_pagu_all > 0 ? round(($total_realisasi_all / $total_pagu_all * 100), 2) : 0;
                                            echo number_format($persen_total, 2) . "%";
                                            ?>
                                        </td>
                                        <td class="text-center">
                                            <?php
                                            $rata_fisik_total = $count_opd > 0 ? round(($total_fisik_all / $count_opd), 2) : 0;
                                            echo number_format($rata_fisik_total, 2) . "%";
                                            ?>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(document).ready(function() {
    // Inisialisasi semua DataTable sekaligus
    $('#tabelBangunan, #tabelFisik, #tabelAnggaran').each(function() {
        var tableId = $(this).attr('id');
        var orderConfig;
        
        // Set konfigurasi pengurutan berdasarkan ID tabel
        if (tableId === 'tabelBangunan') {
            orderConfig = [[2, 'desc'], [3, 'asc']]; // Tahun dan bulan
        } else if (tableId === 'tabelFisik') {
            orderConfig = [[3, 'desc']]; // Tahun
        } else {
            orderConfig = [[2, 'desc']]; // Tahun
        }
        
        $(this).DataTable({
            "responsive": true,
            "lengthChange": true,
            "autoWidth": false,
            "pageLength": 25,
            "order": orderConfig,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        });
    });
    
    // Tambahkan event listener untuk tab
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        // Sesuaikan ukuran kolom DataTable saat tab diaktifkan
        var targetId = $(e.target).attr("href");
        $(targetId + ' table').DataTable().columns.adjust().responsive.recalc();
    });
</script>
