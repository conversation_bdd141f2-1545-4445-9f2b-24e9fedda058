<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();
define('_emonevadmin_',1);

require_once("../../inc/config_admin.php");
require_once("../../inc/database.php");
require_once("../../inc/fungsi.php");

// Process Add
if (isset($_POST['tambah'])) {
    $opd_id = $koneksi_db->sql_escapestr($_POST['opd_id']);
    $tahun = $koneksi_db->sql_escapestr($_POST['tahun']);
    $bulan = $koneksi_db->sql_escapestr($_POST['bulan']);
    $pagu_bangub = $koneksi_db->sql_escapestr($_POST['pagu_bangub']);
    $realisasi_bangub = $koneksi_db->sql_escapestr($_POST['realisasi_bangub']);
    $fisik_bangub = $koneksi_db->sql_escapestr($_POST['fisik_bangub']);
    $keterangan = $koneksi_db->sql_escapestr($_POST['keterangan']);

    // Handle file upload
    $gambar = '';
    if (isset($_FILES['gambar']) && $_FILES['gambar']['error'] == 0) {
        $target_dir = "uploads/bangub/";
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        $file_name = time() . "_bangub." . pathinfo($_FILES["gambar"]["name"], PATHINFO_EXTENSION);
        $target_file = $target_dir . $file_name;
        
        if (move_uploaded_file($_FILES["gambar"]["tmp_name"], $target_file)) {
            $gambar = $target_file;
        }
    }

    $query = $koneksi_db->sql_query("INSERT INTO ".$namadepan."bangub 
        (opd_id, tahun, bulan, pagu_bangub, realisasi_bangub, fisik_bangub, keterangan, gambar) 
        VALUES ('$opd_id', '$tahun', '$bulan', '$pagu_bangub', '$realisasi_bangub', 
        '$fisik_bangub', '$keterangan', '$gambar')");

    if ($query) {
        header("Location: bangub.php?msg=added");
        exit;
    } else {
        header("Location: bangub.php?msg=error");
        exit;
    }
}

// Process Edit
if (isset($_POST['edit'])) {
    $id = $koneksi_db->sql_escapestr($_POST['id']);
    $opd_id = $koneksi_db->sql_escapestr($_POST['opd_id']);
    $tahun = $koneksi_db->sql_escapestr($_POST['tahun']);
    $bulan = $koneksi_db->sql_escapestr($_POST['bulan']);
    $pagu_bangub = $koneksi_db->sql_escapestr($_POST['pagu_bangub']);
    $realisasi_bangub = $koneksi_db->sql_escapestr($_POST['realisasi_bangub']);
    $fisik_bangub = $koneksi_db->sql_escapestr($_POST['fisik_bangub']);
    $keterangan = $koneksi_db->sql_escapestr($_POST['keterangan']);
    $gambar_lama = $_POST['gambar_lama'];

    // Handle file upload
    $gambar = $gambar_lama;
    if (isset($_FILES['gambar']) && $_FILES['gambar']['error'] == 0) {
        $target_dir = "uploads/bangub/";
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        $file_name = time() . "_bangub." . pathinfo($_FILES["gambar"]["name"], PATHINFO_EXTENSION);
        $target_file = $target_dir . $file_name;
        
        if (move_uploaded_file($_FILES["gambar"]["tmp_name"], $target_file)) {
            if ($gambar_lama != '' && file_exists($gambar_lama)) {
                unlink($gambar_lama);
            }
            $gambar = $target_file;
        }
    }

    $query = $koneksi_db->sql_query("UPDATE ".$namadepan."bangub SET 
        opd_id='$opd_id', 
        tahun='$tahun', 
        bulan='$bulan', 
        pagu_bangub='$pagu_bangub', 
        realisasi_bangub='$realisasi_bangub', 
        fisik_bangub='$fisik_bangub', 
        keterangan='$keterangan', 
        gambar='$gambar' 
        WHERE id='$id'");

    if ($query) {
        header("Location: bangub.php?msg=updated");
        exit;
    } else {
        header("Location: bangub.php?msg=error");
        exit;
    }
}

// Process Delete
if (isset($_GET['hapus'])) {
    $id = $koneksi_db->sql_escapestr($_GET['hapus']);
    
    $query_gambar = $koneksi_db->sql_query("SELECT gambar FROM ".$namadepan."bangub WHERE id='$id'");
    $data_gambar = $koneksi_db->sql_fetchrow($query_gambar);
    
    $query = $koneksi_db->sql_query("DELETE FROM ".$namadepan."bangub WHERE id='$id'");
    
    if ($query) {
        if ($data_gambar['gambar'] != '' && file_exists($data_gambar['gambar'])) {
            unlink($data_gambar['gambar']);
        }
        header("Location: bangub.php?msg=deleted");
        exit;
    } else {
        header("Location: bangub.php?msg=error");
        exit;
    }
}