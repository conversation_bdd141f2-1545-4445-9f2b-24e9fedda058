<?php
if (!defined('_emonevadmin_')) {
    header("Location: index.php");
    exit;
}

// Query untuk mendapatkan data realisasi bulanan
$tahun_aktif = date('Y');
$query = "SELECT
    o.nm_opd,
    SUM(CASE WHEN pr.bulan = 1 THEN pr.realisasi ELSE 0 END) as jan,
    SUM(CASE WHEN pr.bulan = 2 THEN pr.realisasi ELSE 0 END) as feb,
    SUM(CASE WHEN pr.bulan = 3 THEN pr.realisasi ELSE 0 END) as mar,
    SUM(CASE WHEN pr.bulan = 4 THEN pr.realisasi ELSE 0 END) as apr,
    SUM(CASE WHEN pr.bulan = 5 THEN pr.realisasi ELSE 0 END) as mei,
    SUM(CASE WHEN pr.bulan = 6 THEN pr.realisasi ELSE 0 END) as jun,
    SUM(CASE WHEN pr.bulan = 7 THEN pr.realisasi ELSE 0 END) as jul,
    SUM(CASE WHEN pr.bulan = 8 THEN pr.realisasi ELSE 0 END) as ags,
    SUM(CASE WHEN pr.bulan = 9 THEN pr.realisasi ELSE 0 END) as sep,
    SUM(CASE WHEN pr.bulan = 10 THEN pr.realisasi ELSE 0 END) as okt,
    SUM(CASE WHEN pr.bulan = 11 THEN pr.realisasi ELSE 0 END) as nov,
    SUM(CASE WHEN pr.bulan = 12 THEN pr.realisasi ELSE 0 END) as des,
    SUM(pr.realisasi) as total_realisasi,
    SUM(pr.pagu) as total_pagu
FROM siepra_opd o
LEFT JOIN pagu_realisasi pr ON o.id = pr.opd_id
WHERE pr.tahun = $tahun_aktif
GROUP BY o.id, o.nm_opd
ORDER BY o.nm_opd";

$result = $koneksi_db->sql_query($query);
?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Realisasi Bulanan OPD</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="?page=dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Realisasi Bulanan</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Data Realisasi Bulanan OPD Tahun <?php echo $tahun_aktif; ?></h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                        <i class="fas fa-file-excel mr-2"></i>Export Excel
                    </button>
                    <button type="button" class="btn btn-primary btn-sm ml-2" onclick="printTable()">
                        <i class="fas fa-print mr-2"></i>Cetak
                    </button>
                    <button type="button" class="btn btn-tool ml-2" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="tabelRealisasi">
                        <thead>
                            <tr class="text-center bg-primary">
                                <th rowspan="2" style="vertical-align: middle;">No.</th>
                                <th rowspan="2" style="vertical-align: middle;">Nama OPD</th>
                                <th rowspan="2" style="vertical-align: middle;">Pagu</th>
                                <th colspan="12">Realisasi Bulan</th>
                                <th rowspan="2" style="vertical-align: middle;">Total Realisasi</th>
                                <th rowspan="2" style="vertical-align: middle;">Sisa</th>

                            </tr>
                            <tr class="text-center bg-primary">
                                <th>Jan</th>
                                <th>Feb</th>
                                <th>Mar</th>
                                <th>Apr</th>
                                <th>Mei</th>
                                <th>Jun</th>
                                <th>Jul</th>
                                <th>Ags</th>
                                <th>Sep</th>
                                <th>Okt</th>
                                <th>Nov</th>
                                <th>Des</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $total_per_bulan = array_fill(1, 12, 0);
                            $grand_total_realisasi = 0;
                            $grand_total_pagu = 0;
                            $no = 1; // Inisialisasi nomor urut

                            while ($row = $koneksi_db->sql_fetchrow($result)) {
                                echo "<tr>";
                                echo "<td class='text-center'>{$no}</td>"; // Tampilkan nomor urut
                                echo "<td>{$row['nm_opd']}</td>";
                                echo "<td class='text-right'>" . number_format($row['total_pagu'], 0, ',', '.') . "</td>";

                                // Tampilkan data per bulan
                                foreach (['jan','feb','mar','apr','mei','jun','jul','ags','sep','okt','nov','des'] as $i => $bulan) {
                                    $nilai = !empty($row[$bulan]) ? number_format($row[$bulan], 0, ',', '.') : '-';
                                    $total_per_bulan[$i+1] += !empty($row[$bulan]) ? $row[$bulan] : 0;
                                    echo "<td class='text-right'>{$nilai}</td>";
                                }

                                // Hitung total dan persentase
                                $total_realisasi = $row['total_realisasi'];
                                $sisa = $row['total_pagu'] - $total_realisasi;
                                $persen = $row['total_pagu'] > 0 ? ($total_realisasi / $row['total_pagu'] * 100) : 0;

                                echo "<td class='text-right font-weight-bold'>" . number_format($total_realisasi, 0, ',', '.') . "</td>";
                                echo "<td class='text-right'>" . number_format($sisa, 0, ',', '.') . "</td>";

                                echo "</tr>";

                                $grand_total_realisasi += $total_realisasi;
                                $grand_total_pagu += $row['total_pagu'];
                                $no++; // Increment nomor urut
                            }

                            // Baris total
                            $grand_persen = $grand_total_pagu > 0 ? ($grand_total_realisasi / $grand_total_pagu * 100) : 0;
                            ?>
                            <tr class="bg-light font-weight-bold">
                                <td></td> <!-- Kolom nomor urut kosong untuk baris total -->
                                <td>Total</td>
                                <td class='text-right'><?php echo number_format($grand_total_pagu, 0, ',', '.'); ?></td>
                                <?php
                                foreach ($total_per_bulan as $total) {
                                    echo "<td class='text-right'>" . number_format($total, 0, ',', '.') . "</td>";
                                }
                                ?>
                                <td class='text-right'><?php echo number_format($grand_total_realisasi, 0, ',', '.'); ?></td>
                                <td class='text-right'><?php echo number_format($grand_total_pagu - $grand_total_realisasi, 0, ',', '.'); ?></td>

                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Grafik Realisasi -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Grafik Realisasi Bulanan</h3>
            </div>
            <div class="card-body">
                <canvas id="realisasiChart" style="min-height: 400px;"></canvas>
            </div>
        </div>
    </div>
</section>

<!-- Tambahkan CSS untuk print -->
<style type="text/css" media="print">
@media print {
    .no-print, .no-print * {
        display: none !important;
    }
    .card {
        box-shadow: none !important;
        border: none !important;
    }
    .table-bordered td, .table-bordered th {
        border: 1px solid #000 !important;
    }
    body {
        margin: 0;
        padding: 15px;
    }
    .content-header, .main-header, .main-sidebar, .main-footer {
        display: none !important;
    }
    .content-wrapper {
        margin-left: 0 !important;
        padding: 0 !important;
    }
}
</style>

<!-- Tambahkan script untuk export dan print -->
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script>
$(document).ready(function() {
    // Inisialisasi DataTable
    $('#tabelRealisasi').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print"],
        "pageLength": 25,
        "order": [[0, 'asc']],
        "scrollX": true,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    }).buttons().container().appendTo('#tabelRealisasi_wrapper .col-md-6:eq(0)');

    // Inisialisasi Chart
    var ctx = document.getElementById('realisasiChart').getContext('2d');
    var myChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
            datasets: [{
                label: 'Total Realisasi per Bulan',
                data: [<?php echo implode(',', $total_per_bulan); ?>],
                backgroundColor: 'rgba(60,141,188,0.2)',
                borderColor: 'rgba(60,141,188,1)',
                borderWidth: 2,
                pointRadius: 4,
                pointBackgroundColor: 'rgba(60,141,188,1)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Realisasi: Rp ' + new Intl.NumberFormat('id-ID').format(context.raw);
                        }
                    }
                }
            }
        }
    });
});

// Fungsi untuk export ke Excel
function exportToExcel() {
    // Buat workbook baru
    var wb = XLSX.utils.book_new();

    // Data untuk Excel
    var data = [
        ['REALISASI BULANAN OPD TAHUN <?php echo $tahun_aktif; ?>'],
        [], // Baris kosong
        ['No.', 'Nama OPD', 'Pagu', 'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des', 'Total Realisasi', 'Sisa']
    ];

    // Ambil data dari tabel
    $('#tabelRealisasi tbody tr').each(function() {
        var row = [];
        $(this).find('td').each(function() {
            // Hapus format angka dan konversi ke number jika perlu
            var value = $(this).text().replace(/\./g, '').replace(/,/g, '.');
            if (!isNaN(value) && value !== '-') {
                value = parseFloat(value);
            }
            row.push(value);
        });
        data.push(row);
    });

    // Buat worksheet
    var ws = XLSX.utils.aoa_to_sheet(data);

    // Styling worksheet
    ws['!merges'] = [
        {s: {r: 0, c: 0}, e: {r: 0, c: 16}} // Merge cells untuk judul (ditambah 1 untuk kolom No.)
    ];

    // Atur lebar kolom
    var wscols = [
        {wch: 8},  // No.
        {wch: 40}, // Nama OPD
        {wch: 15}, // Pagu
        {wch: 15}, // Jan
        {wch: 15}, // Feb
        {wch: 15}, // Mar
        {wch: 15}, // Apr
        {wch: 15}, // Mei
        {wch: 15}, // Jun
        {wch: 15}, // Jul
        {wch: 15}, // Ags
        {wch: 15}, // Sep
        {wch: 15}, // Okt
        {wch: 15}, // Nov
        {wch: 15}, // Des
        {wch: 15}, // Total
        {wch: 15}  // Sisa
    ];
    ws['!cols'] = wscols;

    // Tambahkan worksheet ke workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Realisasi Bulanan');

    // Generate file Excel dan download
    XLSX.writeFile(wb, 'Realisasi_Bulanan_OPD_<?php echo $tahun_aktif; ?>.xlsx');
}

// Fungsi untuk mencetak
function printTable() {
    // Simpan konten asli body
    var originalContents = document.body.innerHTML;

    // Siapkan konten untuk dicetak
    var printContents = `
        <div style="text-align: center; margin-bottom: 20px;">
            <h3>REALISASI BULANAN OPD TAHUN <?php echo $tahun_aktif; ?></h3>
            <h4>BAGIAN ADMINISTRASI PEMBANGUNAN SETDA KOTA PALEMBANG</h4>
        </div>
    `;

    // Tambahkan tabel
    printContents += document.getElementById('tabelRealisasi').outerHTML;

    // Ganti konten body dengan konten yang akan dicetak
    document.body.innerHTML = printContents;

    // Tambahkan style untuk tabel saat dicetak
    var style = document.createElement('style');
    style.innerHTML = `
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid black; padding: 5px; }
        th { background-color: #f4f4f4; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        @media print {
            @page { size: landscape; }
        }
    `;
    document.head.appendChild(style);

    // Cetak
    window.print();

    // Kembalikan konten asli
    document.body.innerHTML = originalContents;

    // Reinisialisasi DataTable dan Chart
    $(document).ready(function() {
        // Inisialisasi ulang DataTable
        $('#tabelRealisasi').DataTable({
            "responsive": true,
            "lengthChange": true,
            "autoWidth": false,
            "buttons": ["copy", "csv", "excel", "pdf", "print"],
            "pageLength": 25,
            "order": [[0, 'asc']],
            "scrollX": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        }).buttons().container().appendTo('#tabelRealisasi_wrapper .col-md-6:eq(0)');

        // Inisialisasi ulang Chart
        var ctx = document.getElementById('realisasiChart').getContext('2d');
        var myChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
                datasets: [{
                    label: 'Total Realisasi per Bulan',
                    data: [<?php echo implode(',', $total_per_bulan); ?>],
                    backgroundColor: 'rgba(60,141,188,0.2)',
                    borderColor: 'rgba(60,141,188,1)',
                    borderWidth: 2,
                    pointRadius: 4,
                    pointBackgroundColor: 'rgba(60,141,188,1)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Realisasi: Rp ' + new Intl.NumberFormat('id-ID').format(context.raw);
                            }
                        }
                    }
                }
            }
        });
    });
}
</script>