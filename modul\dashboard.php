<?php
if (!defined('_emonevadmin_')) {
    header("Location: ../index.php");
    exit;
}

// Get total OPD
$total_opd = $koneksi_db->sql_query("SELECT COUNT(*) as total FROM ".$namadepan."opd WHERE aktif='y'");
$row_opd = $koneksi_db->sql_fetchrow($total_opd);

// Get current month and year
$bulan = date('m');
$tahun = date('Y');

// Get top 5 OPD by realization percentage
$top_opd_query = "SELECT 
    o.id,
    o.nm_opd,
    COALESCE(SUM(p.pagu), 0) as total_pagu,
    COALESCE(SUM(p.realisasi), 0) as total_realisasi,
    CASE 
        WHEN SUM(p.pagu) > 0 THEN ROUND((SUM(p.realisasi) / SUM(p.pagu) * 100), 2)
        ELSE 0 
    END as percentage,
    CASE 
        WHEN SUM(p.pagu) > 0 THEN ROUND((SUM(p.fisik) / SUM(p.pagu) * 100), 2)
        ELSE 0 
    END as rata_fisik
FROM ".$namadepan."opd o
LEFT JOIN pagu_realisasi p ON o.id = p.opd_id 
    AND p.tahun = '$tahun'
    AND p.bulan = '$bulan'
WHERE o.aktif = 'y'
GROUP BY o.id, o.nm_opd
HAVING total_pagu > 0
ORDER BY percentage DESC
LIMIT 5";

$top_opd = $koneksi_db->sql_query($top_opd_query);

// Get monthly realization trend
$monthly_trend_query = "SELECT 
    m.bulan,
    COALESCE(SUM(p.pagu), 0) as total_pagu,
    COALESCE(SUM(p.realisasi), 0) as total_realisasi,
    CASE 
        WHEN SUM(p.pagu) > 0 THEN ROUND((SUM(p.fisik) / SUM(p.pagu) * 100), 2)
        ELSE 0 
    END as rata_fisik
FROM (
    SELECT 1 as bulan UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4
    UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8
    UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12
) m
LEFT JOIN pagu_realisasi p ON m.bulan = p.bulan AND p.tahun = '$tahun'
GROUP BY m.bulan
ORDER BY m.bulan";

$monthly_trend = $koneksi_db->sql_query($monthly_trend_query);

// Query untuk data grafik
$chart_query = "SELECT 
    bulan,
    CASE 
        WHEN SUM(pagu) > 0 THEN ROUND((SUM(realisasi) / SUM(pagu) * 100), 2)
        ELSE 0 
    END as persen_keuangan,
    CASE 
        WHEN SUM(pagu) > 0 THEN ROUND((SUM(fisik) / SUM(pagu) * 100), 2)
        ELSE 0 
    END as persen_fisik
FROM pagu_realisasi 
WHERE tahun = '$tahun'
GROUP BY bulan
ORDER BY bulan";

$chart_result = $koneksi_db->sql_query($chart_query);
$keuangan_data = array_fill(0, 12, 0);
$fisik_data = array_fill(0, 12, 0);

while($row = $koneksi_db->sql_fetchrow($chart_result)) {
    $bulan_index = (int)$row['bulan'] - 1;
    $keuangan_data[$bulan_index] = round($row['persen_keuangan'], 2);
    $fisik_data[$bulan_index] = round($row['persen_fisik'], 2);
}

// Get summary data
$summary_query = "SELECT 
    COALESCE(SUM(p.pagu), 0) as total_pagu,
    COALESCE(SUM(p.realisasi), 0) as total_realisasi,
    COALESCE(AVG(p.fisik), 0) as rata_fisik
FROM pagu_realisasi p 
WHERE p.tahun = '$tahun' AND p.bulan = '$bulan'";

$summary_result = $koneksi_db->sql_query($summary_query);
$summary = $koneksi_db->sql_fetchrow($summary_result);

$realisasi_percentage = $summary['total_pagu'] > 0 ? 
    ($summary['total_realisasi'] / $summary['total_pagu'] * 100) : 0;
?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Dashboard</h1>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-info elevation-1"><i class="fas fa-building"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total OPD</span>
                        <span class="info-box-number"><?php echo number_format($row_opd['total']); ?></span>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-chart-line"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Rata-rata Realisasi Keuangan</span>
                        <span class="info-box-number" id="avgRealisasi">Loading...</span>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-tasks"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Rata-rata Fisik</span>
                        <span class="info-box-number" id="avgFisik">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Top 5 OPD -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Top 5 OPD - Realisasi Tertinggi</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>OPD</th>
                                        <th>Keuangan</th>
                                        <th>Fisik</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($row = $koneksi_db->sql_fetchrow($top_opd)) { ?>
                                    <tr>
                                        <td><?php echo $row['nm_opd']; ?></td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-primary" role="progressbar" 
                                                     style="width: <?php echo number_format($row['percentage'],1); ?>%">
                                                    <?php echo number_format($row['percentage'],1); ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: <?php echo number_format($row['rata_fisik'],1); ?>%">
                                                    <?php echo number_format($row['rata_fisik'],1); ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="?page=realisasi_detail&opd_id=<?php echo $row['id']; ?>" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Trend Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Trend Realisasi Bulanan</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="realisasiChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(document).ready(function() {
    // Debug: cek apakah Chart tersedia
    if (typeof Chart === 'undefined') {
        console.error('Chart.js tidak dimuat!');
        return;
    }

    // Debug: cek data
    console.log('Data Keuangan:', <?php echo json_encode($keuangan_data); ?>);
    console.log('Data Fisik:', <?php echo json_encode($fisik_data); ?>);

    var ctx = document.getElementById('realisasiChart');
    if (!ctx) {
        console.error('Canvas tidak ditemukan!');
        return;
    }

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
            datasets: [{
                label: 'Keuangan (%)',
                data: <?php echo json_encode($keuangan_data); ?>,
                borderColor: '#36a2eb',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                borderWidth: 2,
                fill: true
            }, {
                label: 'Fisik (%)',
                data: <?php echo json_encode($fisik_data); ?>,
                borderColor: '#ff6384',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });

    // Update info boxes
    $('#avgRealisasi').text('<?php echo number_format($realisasi_percentage, 1); ?>%');
    $('#avgFisik').text('<?php echo number_format($summary['rata_fisik'], 1); ?>%');
});
</script> 