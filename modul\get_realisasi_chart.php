<?php
if(!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    exit('No direct script access allowed');
}

include ("../../../inc/config_opd.php");
include ("../../../inc/database.php");

$tahun = date('Y');
$data = array();

// Get data realisasi per bulan
for($i=1; $i<=12; $i++) {
    $query = $koneksi_db->sql_query("SELECT AVG(realisasi_keuangan) as rata_realisasi 
                                    FROM ".$namadepan."realisasi 
                                    WHERE MONTH(tgl_input)='$i' 
                                    AND YEAR(tgl_input)='$tahun'");
    $row = $koneksi_db->sql_fetchrow($query);
    $data[] = $row['rata_realisasi'] ? round($row['rata_realisasi'], 2) : 0;
}

echo json_encode($data);
?> 