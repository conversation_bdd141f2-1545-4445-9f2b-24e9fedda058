<?php
if (!defined('_emonevadmin_')) {
    header("Location: ../index.php");
    exit;
}

$tahun = date('Y');
$bulan = isset($_GET['bulan']) ? (int)$_GET['bulan'] : (int)date('m');

// Array nama bulan
$nama_bulan = array(
    1 => '<PERSON><PERSON>ri', 
    2 => '<PERSON><PERSON><PERSON>', 
    3 => 'Maret', 
    4 => 'April', 
    5 => 'Mei', 
    6 => 'Juni',
    7 => 'Juli', 
    8 => 'Agustus', 
    9 => 'September', 
    10 => 'Oktober', 
    11 => 'November', 
    12 => 'Desember'
);

// Validasi bulan
if (!isset($nama_bulan[$bulan])) {
    $bulan = (int)date('m');
}

// Query untuk mendapatkan data realisasi bulanan per OPD
$query = "SELECT 
    o.id as opd_id,
    o.nm_opd,
    COALESCE(SUM(p.pagu), 0) as total_pagu,
    COALESCE(SUM(p.realisasi), 0) as total_realisasi,
    CASE 
        WHEN SUM(p.pagu) > 0 THEN ROUND((SUM(p.realisasi) / SUM(p.pagu) * 100), 2)
        ELSE 0 
    END as persen_keuangan,
    CASE 
        WHEN SUM(p.pagu) > 0 THEN ROUND((SUM(p.fisik) / SUM(p.pagu) * 100), 2)
        ELSE 0 
    END as persen_fisik
FROM ".$namadepan."opd o
LEFT JOIN pagu_realisasi p ON o.id = p.opd_id 
    AND p.tahun = '$tahun' 
    AND p.bulan = '$bulan'
WHERE o.aktif = 'y'
GROUP BY o.id, o.nm_opd
ORDER BY o.nm_opd";

$result = $koneksi_db->sql_query($query);
?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Realisasi Bulanan OPD</h1>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Filter Bulan -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="">
                    <input type="hidden" name="page" value="realisasi_bulanan">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Pilih Bulan</label>
                                <select name="bulan" class="form-control" onchange="this.form.submit()">
                                    <?php
                                    foreach($nama_bulan as $i => $bln) {
                                        $selected = ($i == $bulan) ? 'selected' : '';
                                        echo "<option value='$i' $selected>$bln</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabel Realisasi -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Data Realisasi Bulan <?php echo isset($nama_bulan[$bulan]) ? $nama_bulan[$bulan] : ''; ?> <?php echo $tahun; ?></h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="realisasiTable">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama OPD</th>
                                <th>Pagu</th>
                                <th>Realisasi</th>
                                <th>Sisa</th>
                                <th>Keuangan (%)</th>
                                <th>Fisik (%)</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $no = 1;
                            $total_pagu = 0;
                            $total_realisasi = 0;
                            
                            while($row = $koneksi_db->sql_fetchrow($result)) { 
                                $total_pagu += $row['total_pagu'];
                                $total_realisasi += $row['total_realisasi'];
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $row['nm_opd']; ?></td>
                                <td class="text-right"><?php echo number_format($row['total_pagu'],0,',','.'); ?></td>
                                <td class="text-right"><?php echo number_format($row['total_realisasi'],0,',','.'); ?></td>
                                <td class="text-right"><?php echo number_format($row['total_pagu'] - $row['total_realisasi'],0,',','.'); ?></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar bg-primary" role="progressbar" 
                                             style="width: <?php echo $row['persen_keuangan']; ?>%">
                                            <?php echo number_format($row['persen_keuangan'], 2); ?>%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: <?php echo $row['persen_fisik']; ?>%">
                                            <?php echo number_format($row['persen_fisik'], 2); ?>%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <a href="?page=realisasi_detail&opd_id=<?php echo $row['opd_id']; ?>" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Detail
                                    </a>
                                </td>
                            </tr>
                            <?php } ?>
                        </tbody>
                        <tfoot>
                            <tr class="bg-light font-weight-bold">
                                <td colspan="2">TOTAL</td>
                                <td class="text-right"><?php echo number_format($total_pagu,0,',','.'); ?></td>
                                <td class="text-right"><?php echo number_format($total_realisasi,0,',','.'); ?></td>
                                <td class="text-right"><?php echo number_format($total_pagu - $total_realisasi,0,',','.'); ?></td>
                                <td colspan="3">
                                    <?php 
                                    $total_persen = $total_pagu > 0 ? ($total_realisasi / $total_pagu * 100) : 0;
                                    echo number_format($total_persen, 2) . '%';
                                    ?>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(document).ready(function() {
    $('#realisasiTable').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "pageLength": 25,
        "dom": 'Bfrtip',
        "buttons": [
            'excel', 'pdf', 'print'
        ]
    });
});
</script> 