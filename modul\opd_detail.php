<?php
if (!defined('_emonevadmin_')) {
    header("Location: index.php");
    exit;
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "<script>
        alert('ID OPD tidak ditemukan');
        window.location.href = '?page=opd_management';
    </script>";
    exit;
}

$opd_id = (int)$_GET['id'];

// Get OPD data
$opd_query = "SELECT * FROM siepra_opd WHERE id = $opd_id";
$opd_result = $koneksi_db->sql_query($opd_query);
$opd = $koneksi_db->sql_fetchrow($opd_result);

if (!$opd) {
    echo "<script>
        alert('Data OPD tidak ditemukan');
        window.location.href = '?page=opd_management';
    </script>";
    exit;
}

// Get data from tb_bangub
$bangub_query = "SELECT * FROM tb_bangub WHERE opd_id = $opd_id ORDER BY tahun DESC, bulan DESC";
$bangub_result = $koneksi_db->sql_query($bangub_query);

// Get data from fisikmj
$fisik_query = "SELECT * FROM fisikmj WHERE opd_id = $opd_id ORDER BY tahun DESC";
$fisik_result = $koneksi_db->sql_query($fisik_query);

// Get realization data
$realisasi_query = "SELECT
    bulan,
    tahun,
    pagu,
    realisasi,
    fisik,
    CASE
        WHEN pagu > 0 THEN ROUND((realisasi / pagu * 100), 2)
        ELSE 0
    END as persen_keuangan
FROM pagu_realisasi
WHERE opd_id = $opd_id
ORDER BY tahun DESC, bulan DESC";
$realisasi_result = $koneksi_db->sql_query($realisasi_query);

// Array nama bulan
$nama_bulan = array(
    1 => 'Januari',
    2 => 'Februari',
    3 => 'Maret',
    4 => 'April',
    5 => 'Mei',
    6 => 'Juni',
    7 => 'Juli',
    8 => 'Agustus',
    9 => 'September',
    10 => 'Oktober',
    11 => 'November',
    12 => 'Desember'
);
?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Detail OPD</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="?page=dashboard">Home</a></li>
                    <li class="breadcrumb-item"><a href="?page=opd_management">Manajemen OPD</a></li>
                    <li class="breadcrumb-item active">Detail OPD</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- OPD Profile Card -->
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h3 class="card-title">Profil OPD</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body box-profile">
                <div class="text-center">
                    <?php if (!empty($opd['logo'])): ?>
                        <img class="profile-user-img img-fluid img-circle"
                             src="../../uploads/logo_opd/<?php echo $opd['logo']; ?>"
                             alt="Logo OPD">
                    <?php else: ?>
                        <img class="profile-user-img img-fluid img-circle"
                             src="../../assets/images/no-image.png"
                             alt="Logo OPD">
                    <?php endif; ?>
                </div>

                <h3 class="profile-username text-center"><?php echo $opd['nm_opd']; ?></h3>
                <p class="text-muted text-center">
                    <?php echo $opd['aktif'] == 'y' ? '<span class="badge badge-success">Aktif</span>' : '<span class="badge badge-danger">Non-Aktif</span>'; ?>
                </p>

                <ul class="list-group list-group-unbordered mb-3">
                    <li class="list-group-item">
                        <b>Alamat</b> <span class="float-right"><?php echo $opd['alamat']; ?></span>
                    </li>
                    <li class="list-group-item">
                        <b>Telepon</b> <span class="float-right"><?php echo $opd['telp']; ?></span>
                    </li>
                    <li class="list-group-item">
                        <b>Email</b> <span class="float-right"><?php echo $opd['email']; ?></span>
                    </li>
                    <li class="list-group-item">
                        <b>Website</b> <span class="float-right">
                            <a href="<?php echo $opd['website']; ?>" target="_blank"><?php echo $opd['website']; ?></a>
                        </span>
                    </li>
                </ul>

                <a href="?page=opd_management&action=toggle_status&id=<?php echo $opd_id; ?>" class="btn btn-warning btn-block">
                    <i class="fas fa-sync-alt"></i>
                    <?php echo $opd['aktif'] == 'y' ? 'Nonaktifkan OPD' : 'Aktifkan OPD'; ?>
                </a>
            </div>
        </div>

        <!-- Tabs for different data -->
        <div class="card">
            <div class="card-header p-2">
                <ul class="nav nav-pills">
                    <li class="nav-item">
                        <a class="nav-link active" href="#bangunan" data-toggle="tab">Data Bangunan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#fisik" data-toggle="tab">Data Fisik</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#realisasi" data-toggle="tab">Realisasi Anggaran</a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- Bangunan Tab -->
                    <div class="tab-pane active" id="bangunan">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="bangunanTable">
                                <thead>
                                    <tr class="text-center bg-primary">
                                        <th>No.</th>
                                        <th>Tahun</th>
                                        <th>Bulan</th>
                                        <th>Pagu</th>
                                        <th>Realisasi</th>
                                        <th>Sisa Anggaran</th>
                                        <th>Realisasi (%)</th>
                                        <th>Sisa (%)</th>
                                        <th>Fisik (%)</th>
                                        <th>Keterangan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    while ($row = $koneksi_db->sql_fetchrow($bangub_result)) {
                                        echo "<tr>";
                                        echo "<td class='text-center'>{$no}</td>";
                                        echo "<td class='text-center'>{$row['tahun']}</td>";
                                        echo "<td class='text-center'>{$nama_bulan[$row['bulan']]}</td>";
                                        echo "<td class='text-right'>" . number_format($row['pagu_bangub'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['realisasi_bangub'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['sisa_anggaran'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-primary' role='progressbar' style='width: {$row['persen_realisasi']}%'>";
                                        echo number_format($row['persen_realisasi'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-warning' role='progressbar' style='width: {$row['persen_sisa']}%'>";
                                        echo number_format($row['persen_sisa'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-success' role='progressbar' style='width: {$row['fisik_bangub']}%'>";
                                        echo number_format($row['fisik_bangub'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "<td>{$row['keterangan']}</td>";
                                        echo "</tr>";
                                        $no++;
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Fisik Tab -->
                    <div class="tab-pane" id="fisik">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="fisikTable">
                                <thead>
                                    <tr class="text-center bg-primary">
                                        <th>No.</th>
                                        <th>Kode Rekening</th>
                                        <th>Tahun</th>
                                        <th>Uraian</th>
                                        <th>Nilai DPA</th>
                                        <th>Nilai Kontrak</th>
                                        <th>Realisasi</th>
                                        <th>Sisa</th>
                                        <th>Progress (%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    while ($row = $koneksi_db->sql_fetchrow($fisik_result)) {
                                        $sisa = $row['nilai_dpa'] - $row['realisasi'];
                                        $progress = $row['nilai_dpa'] > 0 ? round(($row['realisasi'] / $row['nilai_dpa'] * 100), 2) : 0;

                                        echo "<tr>";
                                        echo "<td class='text-center'>{$no}</td>";
                                        echo "<td>{$row['kode_rekening']}</td>";
                                        echo "<td class='text-center'>{$row['tahun']}</td>";
                                        echo "<td>{$row['uraian']}</td>";
                                        echo "<td class='text-right'>" . number_format($row['nilai_dpa'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['nilai_kontrak'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['realisasi'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($sisa, 0, ',', '.') . "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-success' role='progressbar' style='width: {$progress}%'>";
                                        echo number_format($progress, 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "</tr>";
                                        $no++;
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Realisasi Tab -->
                    <div class="tab-pane" id="realisasi">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="realisasiTable">
                                <thead>
                                    <tr class="text-center bg-primary">
                                        <th>No.</th>
                                        <th>Periode</th>
                                        <th>Pagu</th>
                                        <th>Realisasi</th>
                                        <th>Sisa</th>
                                        <th>Keuangan (%)</th>
                                        <th>Fisik (%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    while ($row = $koneksi_db->sql_fetchrow($realisasi_result)) {
                                        $periode = $nama_bulan[$row['bulan']] . ' ' . $row['tahun'];
                                        $sisa = $row['pagu'] - $row['realisasi'];

                                        echo "<tr>";
                                        echo "<td class='text-center'>{$no}</td>";
                                        echo "<td>{$periode}</td>";
                                        echo "<td class='text-right'>" . number_format($row['pagu'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($row['realisasi'], 0, ',', '.') . "</td>";
                                        echo "<td class='text-right'>" . number_format($sisa, 0, ',', '.') . "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-primary' role='progressbar' style='width: {$row['persen_keuangan']}%'>";
                                        echo number_format($row['persen_keuangan'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "<td class='text-center'>";
                                        echo "<div class='progress'>";
                                        echo "<div class='progress-bar bg-success' role='progressbar' style='width: {$row['fisik']}%'>";
                                        echo number_format($row['fisik'], 2) . "%";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</td>";
                                        echo "</tr>";
                                        $no++;
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(document).ready(function() {
    // Initialize DataTables
    $('#bangunanTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print"],
        "pageLength": 10,
        "order": [[1, 'desc'], [2, 'desc']], // Order by year and month
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });

    $('#fisikTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print"],
        "pageLength": 10,
        "order": [[2, 'desc']], // Order by year
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });

    $('#realisasiTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print"],
        "pageLength": 10,
        "order": [[1, 'desc']], // Order by period
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});
</script>
