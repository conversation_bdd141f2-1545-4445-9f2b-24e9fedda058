<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Chart.js</title>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <h1>Debug Chart.js Loading</h1>
    
    <div style="width: 400px; height: 300px;">
        <canvas id="testChart"></canvas>
    </div>
    
    <div id="debug-info"></div>
    
    <script>
        $(document).ready(function() {
            var debugInfo = $('#debug-info');
            
            // Check jQuery
            debugInfo.append('<p>jQuery loaded: ' + (typeof $ !== 'undefined') + '</p>');
            
            // Check Chart.js
            debugInfo.append('<p>Chart.js loaded: ' + (typeof Chart !== 'undefined') + '</p>');
            
            if (typeof Chart !== 'undefined') {
                debugInfo.append('<p>Chart.js version: ' + Chart.version + '</p>');
                
                // Test simple chart
                var ctx = document.getElementById('testChart');
                if (ctx) {
                    try {
                        new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: ['Jan', 'Feb', 'Mar'],
                                datasets: [{
                                    label: 'Test Data',
                                    data: [10, 20, 30],
                                    backgroundColor: 'rgba(54, 162, 235, 0.8)'
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false
                            }
                        });
                        debugInfo.append('<p style="color: green;">✓ Test chart created successfully!</p>');
                    } catch (error) {
                        debugInfo.append('<p style="color: red;">✗ Error creating test chart: ' + error.message + '</p>');
                    }
                } else {
                    debugInfo.append('<p style="color: red;">✗ Canvas element not found!</p>');
                }
            } else {
                debugInfo.append('<p style="color: red;">✗ Chart.js not loaded!</p>');
            }
        });
    </script>
</body>
</html>