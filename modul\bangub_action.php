<?php
session_start();
if (!isset($_SESSION['admin_siepra'])) {
    die('<PERSON><PERSON><PERSON> ditolak');
}

require_once("../../../inc/config_admin.php");
require_once("../../../inc/database.php");

header('Content-Type: application/json');

$act = isset($_GET['act']) ? $_GET['act'] : '';

switch($act) {
    case 'tambah':
        $id_opd = $_POST['id_opd'];
        $tahun = $_POST['tahun'];
        $jumlah_bantuan = $_POST['jumlah_bantuan'];
        $keterangan = $_POST['keterangan'];
        $status = $_POST['status'];
        
        $query = "INSERT INTO ".$namadepan."bangub (id_opd, tahun, jumlah_bantuan, keterangan, status) 
                 VALUES ('$id_opd', '$tahun', '$jumlah_bantuan', '$keterangan', '$status')";
        
        if($koneksi_db->sql_query($query)) {
            echo json_encode(['status' => 'success']);
        } else {
            echo json_encode(['status' => 'error']);
        }
        break;

    case 'hapus':
        $id = $_POST['id'];
        
        $query = "DELETE FROM ".$namadepan."bangub WHERE id = '$id'";
        
        if($koneksi_db->sql_query($query)) {
            echo json_encode(['status' => 'success']);
        } else {
            echo json_encode(['status' => 'error']);
        }
        break;

    default:
        echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
}
?>